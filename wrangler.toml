main = "./dist/index.mjs"
name = "worker-typescript-api-pms" # <==== เปลี่ยนตรงนี้
compatibility_date = "2023-01-27"
node_compat = true
zone_id = '6f8fd5e414b56f90500b00460a11cebf'
account_id = '71db72daf6438fb1c04a3c4fbceff9ae'
# zone_id = 'e5d29a27eea94d1e1e26b7ef455fd84b' #<--- ags.im
# account_id = '666cc352ad48ce987fc321a17b755642' #<--- ags.im

[build]
command = "npm install && npm run build && cp ./src/driver/**/*.wasm ./dist" #<--- mysql require

[vars]
ENVIRONMENT = 'dev'
DOCKER =  "https://kubernetes-ci.agsdocker.com/"
URL_GETBALANCELIKE = "https://new.likepoint.io/getBalanceByphoneNumber"
TUNNEL_HOST = "https://tunnelaws.agilesoftgroup.com"                                  #<--- mysql require
CF_CLIENT_ID = "0bf2bd876ed819d54fc49d11dfb709d6.access"                              #<--- mysql require
CF_CLIENT_SECRET = "8bea9fdd917637cd91aad6472de5df9cc0f6f39a67bb46839015e6d095b3d452" #<--- mysql require
DB_SECRET = "Hz4PzDNFEfAE9L7jGcBKpBXU"  
MY_SQL_CLIENT_CON = "https://agilesoftgroup.com/mysql/query2"
HCS_URL = "https://68wtlqk0s2.execute-api.ap-southeast-1.amazonaws.com/dev/submitMessageTopic"
HCS_X_API = "sCzIzOuTio8LOr9OigCJj4nfuwxWudmQ4WCvyfiP"
DB_USER = "webapp_admin" ## <==== ตั้งเอง
DB_PASS = "9N61KigjbJ2llK155G" ## <==== ตั้งเอง
R_USER = "MAPP_PMS" ## <==== ตั้งเอง
R_TOKEN = "DZp#XbB3%#qs6I94" ## <==== ตั้งเอง แต่อย่าให้ใครรู้
API_URL_LIKEPOINT = "https://api2.likepoint.io"
#API_URL_LIKEPOINT = "https://api2.likepoint.io/transactions-activity/pay-poi-in-app"
API_KEY_LIKEPOINT = "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu"
MERCHANT_ID = "d38c25f9-3684-4d5a-82ca-2c821abc9189"
KEY_POI_DOWNLOAD = "78653016-e870-4480-9436-f83c5e188cc2"
KEY_POI_FEEDBACK = "07c595f9-4f66-4e63-8ede-03cb9e1d0c6b"
GOOGLE_OAUTH2_URL = "https://oauth2.googleapis.com/token"
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
CLIENT_EMAIL = "<EMAIL>"
GET_ENCYPT_KEY = "TMwmHRyyBMdQa79QppPvdTXm80OoW2ufTQUQwGRkI9u7i4tWxQvmvrBjmVyAvyYJ7W9kX1OMw6M4zVPsyHmlXtH5MYDqV1JBSMqmHxUGZLdZz6BhwRwR0BwhP3lUxHdF"
PAY_HOOK_URL = "https://n8n-deploy.agilesoftgroup.com/webhook/7dc93b1a-a38f-40ac-9930-0b18b68949e4"
PAY_HOOK_KEY = "6fb7f9ec-9a55-4d0f-8f64-116cb60aafa8"

[triggers]
crons = ["0 02 * * *"]

## ข้างล่างให้มี ตัวแปรเหมือนกัน ยักเว้น DOCKER ให้ไม่เหมือนกันแหละดีแล้ว ถ้าเปิด product แล้วไม่ได้ ก็มาดูตรงนี้แหละ
[env.staging]
name = "pms-staging"
vars = { ENVIRONMENT = "staging",DB_USER = "webapp_admin", DB_PASS = "9N61KigjbJ2llK155G", R_USER = "MAPP_PMS", R_TOKEN = "DZp#XbB3%#qs6I94",  DOCKER =  "https://cf-kubernetes.agilesoftgroup.com/", URL_GETBALANCELIKE = "https://new.likepoint.io/getBalanceByphoneNumber", HCS_URL = "https://68wtlqk0s2.execute-api.ap-southeast-1.amazonaws.com/dev/submitMessageTopic", HCS_X_API = "sCzIzOuTio8LOr9OigCJj4nfuwxWudmQ4WCvyfiP", API_URL_LIKEPOINT = "https://api2.likepoint.io", API_KEY_LIKEPOINT = "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu", MERCHANT_ID = "d38c25f9-3684-4d5a-82ca-2c821abc9189", KEY_POI_DOWNLOAD = "78653016-e870-4480-9436-f83c5e188cc2", KEY_POI_FEEDBACK = "07c595f9-4f66-4e63-8ede-03cb9e1d0c6b", GOOGLE_OAUTH2_URL = "https://oauth2.googleapis.com/token", GET_ENCYPT_KEY = "TMwmHRyyBMdQa79QppPvdTXm80OoW2ufTQUQwGRkI9u7i4tWxQvmvrBjmVyAvyYJ7W9kX1OMw6M4zVPsyHmlXtH5MYDqV1JBSMqmHxUGZLdZz6BhwRwR0BwhP3lUxHdF", PAY_HOOK_URL = "https://n8n-deploy.agilesoftgroup.com/webhook/7dc93b1a-a38f-40ac-9930-0b18b68949e4", PAY_HOOK_KEY = "6fb7f9ec-9a55-4d0f-8f64-116cb60aafa8"}
# route = "pms-staging.prachakij.workers.dev/PMS/*"
services = [
  { binding = "pmsbinding2", service = "msbinding2", environment = "production" }
]

[env.production]
name = "pms-api"
vars = { ENVIRONMENT = "production",DB_USER = "webapp_admin", DB_PASS = "9N61KigjbJ2llK155G", R_USER = "MAPP_PMS", R_TOKEN = "DZp#XbB3%#qs6I94", DOCKER =  "https://cf-kubernetes.agilesoftgroup.com/", URL_GETBALANCELIKE = "https://new.likepoint.io/getBalanceByphoneNumber",HCS_URL = "https://68wtlqk0s2.execute-api.ap-southeast-1.amazonaws.com/dev/submitMessageTopic", HCS_X_API = "sCzIzOuTio8LOr9OigCJj4nfuwxWudmQ4WCvyfiP", API_URL_LIKEPOINT = "https://api2.likepoint.io", API_KEY_LIKEPOINT = "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu", TUNNEL_HOST = "https://tunnelaws.agilesoftgroup.com", CF_CLIENT_ID = "0bf2bd876ed819d54fc49d11dfb709d6.access", CF_CLIENT_SECRET = "8bea9fdd917637cd91aad6472de5df9cc0f6f39a67bb46839015e6d095b3d452", DB_SECRET = "Hz4PzDNFEfAE9L7jGcBKpBXU", MY_SQL_CLIENT_CON = "https://agilesoftgroup.com/mysql/query2", MERCHANT_ID = "d38c25f9-3684-4d5a-82ca-2c821abc9189", KEY_POI_DOWNLOAD = "78653016-e870-4480-9436-f83c5e188cc2", KEY_POI_FEEDBACK = "07c595f9-4f66-4e63-8ede-03cb9e1d0c6b", GOOGLE_OAUTH2_URL = "https://oauth2.googleapis.com/token", ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************, CLIENT_EMAIL = "<EMAIL>", GET_ENCYPT_KEY = "TMwmHRyyBMdQa79QppPvdTXm80OoW2ufTQUQwGRkI9u7i4tWxQvmvrBjmVyAvyYJ7W9kX1OMw6M4zVPsyHmlXtH5MYDqV1JBSMqmHxUGZLdZz6BhwRwR0BwhP3lUxHdF", PAY_HOOK_URL = "https://n8n-deploy.agilesoftgroup.com/webhook/7dc93b1a-a38f-40ac-9930-0b18b68949e4", PAY_HOOK_KEY = "6fb7f9ec-9a55-4d0f-8f64-116cb60aafa8"}
route = "agilesoftgroup.com/PMS/*"
services = [
  { binding = "SQLBIND", service = "cf-mysql-staging", environment = "production" }
]
logpush = true
