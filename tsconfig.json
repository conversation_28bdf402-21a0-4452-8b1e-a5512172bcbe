{
  "compilerOptions": {
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "baseUrl": "src",
    "declaration": true,
    "esModuleInterop": true,
    "inlineSourceMap": false,
    "lib": [
      "esnext",
      "dom",
      "dom.iterable"
    ],
    "listEmittedFiles": false,
    "listFiles": false,
    "moduleResolution": "node",
    "noFallthroughCasesInSwitch": true,
    "pretty": true,
    "resolveJsonModule": true,
    "rootDir": "src",
    "skipLibCheck": true,
    "strict": true,
    "traceResolution": false,
    "module": "esnext",
    "outDir": "dist/mjs",
    "target": "esnext",
    "types": [
      "@cloudflare/workers-types"
    ],
  },
  "exclude": ["node_modules", "dist", "**/*.spec.ts"],
  "include": ["src"]
}
// {
//   "compilerOptions": {
//     "target": "esnext",
//     "lib": ["esnext", "webworker"],
//     "alwaysStrict": true,
//     "strict": true,
//     "noEmitOnError": true,
//     "moduleResolution": "node",
//     "outDir": "tscOutDir",
//     "preserveConstEnums": true,
//     "esModuleInterop": true,
//     "allowJs": true,
//     "types": ["@cloudflare/workers-types", "node", "jest"]
//   },
//   "include": ["src"],
//   "exclude": ["node_modules", "dist", "**/*.spec.ts"]
// }