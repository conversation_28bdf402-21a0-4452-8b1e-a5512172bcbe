import {responseError, responseSuccess} from "../help/response";
import ags_authrest from '@agilesoft/type_ags_authrest2' 

class DecodeMiddleware {
    public async DecodeBody(req: any) {
        try {
            // console.log(req.params.phone);
            // console.log(ENV_DEPLOY);c
            
            if (ENV_DEPLOY === "production") {
                console.log("production")
                var Auth = new ags_authrest(R_TOKEN, R_USER) //4
                
                req.data = await Auth.Middleware(req);
                
            }else {
                
                req.data = await req.json();
                
            }
        } catch (error) {
            return await responseError(JSON.stringify(error), 404);
        }
    }

    public async ByPassBody(req: any) {
        try {
            req.data = await req.json();
        } catch (error) {
            return await responseError(JSON.stringify(error), 404);
        }
    }
}

export let DecodeMiddlewareModule = new DecodeMiddleware();