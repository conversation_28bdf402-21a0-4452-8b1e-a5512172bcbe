import { Router } from "itty-router";
import { AncillaryProductModule } from "./modules/AncillaryProduct/controller/ancillary-product-controller";
import cors from "cors";
import { DecodeMiddlewareModule } from "./middleware/decode.middleware";
import { ValidationAncillaryProduct } from "./modules/AncillaryProduct/validate/ancillary-product-validate";
import { LicensePlateModule } from "./modules/CarLicensePlate/controller/license-plate-controller";
import { ValidationLicensePlate } from "./modules/CarLicensePlate/validate/license-plate-validate";
import { ValidationAppointment } from "./modules/Appointment/validate/appointment-validate";
import { AppointmentModule } from "./modules/Appointment/controller/appointment-controller";
import { ValidationNotification } from "./modules/Notification/validate/notification-validate";
import { SendOTPModule } from "./modules/OTP/controller/sendOTP-controller";
import { ValidationBrokenCar } from "./modules/BrokenCar/validate/broken-car-validate";
import { BrokenCarModule } from "./modules/BrokenCar/controller/broken-car-controller";
import { VerifyOTPModule } from "./modules/OTP/controller/verifyOTP-controller";
import { ValidationHomeService } from "./modules/HomeService/validate/HomeService-validate";
import { HomeServiceModule } from "./modules/HomeService/controller/HomeService-controller";
import { ValidationRegister } from "./modules/Register/validate/register-validate";
import { registerModule } from "./modules/Register/controller/register-controller";
import { MRPOIModule } from "./modules/MR/controller/MR-POI-controller";
import { EventModule } from "./modules/Event/controller/event-controller";
import { ValidationEventRegister } from "./modules/Event/varlidate/event-register-validate";
import { EventRegisterModule } from "./modules/Event/controller/event-register-controller";
import { NewCarModule } from "./modules/NewCar/controller/new-car-controller";
import { ValidationNewCar } from "./modules/NewCar/validate/new-car-validate";
import { ValidationLifetimeAppointment } from "./modules/LifetimeAppointment/validate/lifetime-appointment-validate";
import { LifetimeAppointmentModule } from "./modules/LifetimeAppointment/controller/lifetime-appointment-controller";
import { ValidationSaveActivity } from "./modules/SaveActivity/validate/save-activity-validate";
import { SaveActivityModule } from "./modules/SaveActivity/controller/save-activity-controller";
import { GetNewsByIDModule } from "./modules/News/controller/get-news-by-id-controller";
import { GetNewsModule } from "./modules/News/controller/get-news-controller";
import { GetCatalogModule } from "./modules/Catalog/controller/get-catalog-controller";
import { GetPromotionModule } from "./modules/Promotion/controller/get-promotion-controller";
import { LifetimeAppointmentMilesModule } from "./modules/LifetimeAppointment/controller/noti-miles-controller";
import { LifetimeAppointmentRustproofModule } from "./modules/LifetimeAppointment/controller/noti-rustproof-controller";
import { LifetimeAppointmentInsuranceModule } from "./modules/LifetimeAppointment/controller/noti-resInsurance-controller";
import { LifetimeAppointmentPMGModule } from "./modules/LifetimeAppointment/controller/noti-pmg-controller";
import { ValidationCheckProfile } from "./modules/Member/validate/check-profile-validate";
import { CheckProfileModule } from "./modules/Member/controller/check-profile-controller";
import { ValidationCreateNews } from "./modules/News/validate/create-news-validate";
import { createNewsModule } from "./modules/News/controller/create-news-controller";
import { ValidationUpdateNews } from "./modules/News/validate/update-news-validate";
import { updateNewsModule } from "./modules/News/controller/update-news-controller";
import { ValidationPronotion } from "./modules/Promotion/validate/promotion-validate";
import { createPromotionModule } from "./modules/Promotion/controller/create-promotion-controller";
import { updatePromotionModule } from "./modules/Promotion/controller/update-promotion-controller";
import { deletePromotionModule } from "./modules/Promotion/controller/delete-promotion-controller";
import { connectdocker } from "help/connectDataBase";
import { ValidationOTP } from "modules/OTP/validate/OTP-validate";
import { NotificationModule } from "modules/Notification/controller/notification-controller";
import { Notifycrud } from "modules/Notification/controller/notification_crud";
import { ValidationSearchMapMarketing } from "modules/MapMarketing/validate/search-map-marketing-validate";
import { SearchMapMarketingController } from "modules/MapMarketing/controller/search-map-marketing-controller";
import { ValidateLoginTGModule } from "modules/Login/validate/login-telegram-validate";
import { LoginTGModule } from "modules/Login/controller/login-telegram-controller";
import { ValidateRegisterTGModule } from "modules/Register/validate/register-telegram-validate";
import { registerTGModule } from "modules/Register/controller/register-telegram-controller";
import { ValidationAddActivity } from "modules/MapMarketing/validate/add-activity-data-validate";
import { AddActivityDataModule } from "modules/MapMarketing/controller/add-activity-data-controller";
import { DeleteActivityDataModule } from "modules/MapMarketing/controller/delete-activity-data-controller";
import { ValidationMapAppointment } from "modules/Appointment/validate/map-appointment-validate";
import { MapAppointmentModule } from "modules/Appointment/controller/map-appointment-controller";
import { UpdateActivityDataModule } from "./modules/MapMarketing/controller/update-activity-data-controller";
import {
  ValidateUpdateActivity,
  ValidationUpdateActivity,
} from "./modules/MapMarketing/validate/update-activity-data-validate";
import { ValidationDeleteNews } from "modules/News/validate/delete-news-validate";
import { deleteNewsModule } from "modules/News/controller/delete-news-controller";
import { ValidationDeletePromotion } from "modules/Promotion/validate/delete-promotion-validate";
import { ValidationCarRepairStatus } from "modules/CarRepairStatus/validate/get-car-repair-status-validate";
import { GetCarRepairStatusModule } from "modules/CarRepairStatus/controller/get-car-repair-status-controller";
import { ValidationConnectSocial } from "modules/ConnectSocial/validate/connect-social-validate";
import { ConnectSocialModule } from "modules/ConnectSocial/controller/connect-social-controller";
import { UpdatePaymentStatusModule } from "modules/CarRepairStatus/controller/update-payment-status-controller";
import { GarageServiceModule } from "modules/CarRepairStatus/controller/get-garage-service-controller";
import { ValidationGarageService } from "modules/CarRepairStatus/validate/get-garage-service-validate";
import { ValidationAcceptAgreement } from "modules/Member/validate/update-agreement-validate";
import { UpdateAgreementModule } from "modules/Member/controller/update-agreement-controller";
import { ValidationPMSServiceSendCarPost } from "modules/PMSServiceSendCar/validate/pms-service-send-car-post-validate";
import { PMSServiceSendCarPostModule } from "modules/PMSServiceSendCar/controller/pms-service-send-car-post";
import { ValidationFeedBack } from "modules/Feedback/validate/Feedback-validate";
import { FeedbackModule } from "modules/Feedback/controller/Feedback-controller";
import { EstimateColorModule } from "modules/EstimateColor/controller/estimate-color-controller";
import { ValidationEstimateColor } from "modules/EstimateColor/validate/estimate-color-validate";
import { POIPaylikeModule } from "modules/Poi/controller/poi-paylike-controller";
import { ValidationPOIPayLikepoint } from "modules/Poi/validate/poi-paylike-validate";
import { ValidationSparePartsPost } from "modules/Online_spare_parts/validate/spare-parts-post-validate";
import { SparePartsPostModule } from "modules/Online_spare_parts/controller/spare-parts-post";
import { SparePartsGetModule } from "modules/Online_spare_parts/controller/spare-parts-get";
import { RegisterSocialModule } from "./modules/RegisterSocial/controller/registerSocial-controller";
import { ValidationOrderSparePartsPost } from "modules/Online_spare_parts/validate/spare-parts-order-post-validate";
import { OrderSparePartsPostModule } from "modules/Online_spare_parts/controller/spare-parts-order-post";
import { OrderSparePartsGetModule } from "modules/Online_spare_parts/controller/spare-parts-order-get";
import { ValidateAddressSparePartsPost, ValidationAddressSparePartsPost } from "modules/Online_spare_parts/validate/spare-parts-address-post-validate";
import { ShippingAddressesModule } from "modules/Online_spare_parts/controller/spare-parts-address-post";
import { ShippingAddressGetModule } from "modules/Online_spare_parts/controller/spare-parts-address-get";
import { SyncPaymentModule } from "./modules/SyncPayment/controller/sync-payment-controller";
import { ValidateSyncPayment, ValidationSyncPayment } from "./modules/SyncPayment/validate/sync-payment-validate";
import { ValidationCouponPSI } from "modules/CouponPSI/validate/couponPSI-validate";
import { CouponPSIModule } from "modules/CouponPSI/controller/get-conupon-PSI";
import { ValidationCarOwner } from "modules/CouponPSI/validate/carOwner-validate";
import { CarOwnerModule } from "modules/CouponPSI/controller/get-car-owner";
import { EncyptIDModule } from "modules/Encyption/controller/encyptionController";
import { ValidationCouponRC } from "modules/CouponRC/validate/couponRC";
import { GetCouponRCModule } from "modules/CouponRC/controller/get-couponRC";
import { InsertCouponModule } from "modules/CouponRC/controller/insert-couponRC";
import { CheckMRCodeModule } from "modules/MR/controller/checkMRCode-controller";
import { ValidationCheckMRCode } from "modules/MR/validate/check-mr-code-validate";
import { GetHistoryMRCodeModule} from "modules/MR/controller/get-history-mr-code-controller";
import { ValidationGetHistoryMRCode } from "modules/MR/validate/get-history-mr-code-validate";
import { UpdateRefCodeMrModule } from "modules/RefCode/controller/update-ref-code-mr-controller";
import { ValidationUpdateRefCodeMr } from "modules/RefCode/validate/update-ref-code-mr-validate";
import { GetRefCodeModule } from "modules/RefCode/controller/get-refcode-controller";
import { ValidationGetRefCode } from "modules/RefCode/validate/get-refcode-validate";
import { MigrateJobModule } from "modules/MigrateSantoSA/controller/updateData_sansampun_to_saImage";
import { ValidateMigrateJob, ValidationMigrateJob } from "modules/MigrateSantoSA/validate/migrate-validate";
// now let's create a router (note the lack of "new")
const router = Router();

type Environment = {
  readonly TUNNEL_HOST: string;
  readonly CF_CLIENT_ID: string;
  readonly CF_CLIENT_SECRET: string;
};

declare let global: GlobalEnvironment;

interface GlobalEnvironment {
  hcsX: any;
  hcsURL: any;
  dbPass: string;
  dbUser: string;
  SERVICE_API_DOCKER: string;
  ENV_DEPLOY: string;
}

const ENV_DEPLOY = ENVIRONMENT;

global.SERVICE_API_DOCKER = DOCKER;
global.dbUser = DB_USER;
global.dbPass = DB_PASS;
global.ENV_DEPLOY = ENV_DEPLOY;
global.hcsURL = HCS_URL;
global.hcsX = HCS_X_API;

// if (ENV_DEPLOY == 'production') {
//     console.log('production');
//     var Auth = new ags_restauth(R_TOKEN, R_USER);
// }

const allowedOrigins = ["*"];

const options: cors.CorsOptions = {
  origin: allowedOrigins,
};

var corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET,HEAD,POST,PUT,OPTIONS,DELETE",
  "Access-Control-Max-Age": "86400",
};
router.use(cors(options));

// /** Server */

function handleOptions(request) {
  // Make sure the necessary headers are present
  // for this to be a valid pre-flight request
  let headers = request.headers;
  if (
    headers.get("Origin") !== null &&
    headers.get("Access-Control-Request-Method") !== null &&
    headers.get("Access-Control-Request-Headers") !== null
  ) {
    // Handle CORS pre-flight request.
    // If you want to check or reject the requested method + headers
    // you can do that here.
    let respHeaders = {
      ...corsHeaders,
      // Allow all future content Request headers to go back to browser
      // such as Authorization (Bearer) or X-Client-Name-Version
      "Access-Control-Allow-Headers": request.headers.get(
        "Access-Control-Request-Headers"
      ),
    };

    return new Response(null, {
      headers: respHeaders,
    });
  } else {
    // Handle standard OPTIONS request.
    // If you want to allow other HTTP Methods, you can do that here.
    return new Response(null, {
      headers: {
        Allow: "GET, HEAD, POST, OPTIONS, DELETE",
      },
    });
  }
}


router
  // TODO :: TEST
  .post(
    "/mysql/query",
    async (request: Request, env: Environment, ctx: ExecutionContext) => {
      try {
        // const MySQLCon = new Con();
        var result = await connectdocker.connect(
          "CI_Docker_test",
          "SELECT * FROM `test_time` WHERE `Device_name` LIKE ?",
          ["%ddd%"]
        );
        console.log(result);
        return new Response(JSON.stringify(result), {
          headers: {
            "content-type": "application/json;charset=UTF-8",
          },
        });
      } catch (error) {
        // console.log(error);
      }
    }
  )
  // TODO :: TEST

  // TODO :: Ancillary Product
  .get(
    "/PMS/ancillary-product/get-all-ancillary-product",
    DecodeMiddlewareModule.DecodeBody
  )
  .post(
    "/PMS/ancillary-product/create-ancillary-product",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAncillaryProduct.ValidateCreateAncillaryProduct,
    AncillaryProductModule.createAncillaryProduct
  )
  // TODO :: Ancillary Product

  // TODO :: Appointment
  .post(
    "/PMS/appointment/create-appointment",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAppointment.ValidateCreateAppointment,
    AppointmentModule.createAppointment
  )
  .post(
    "/PMS/appointment/create-appointment-map",
    DecodeMiddlewareModule.DecodeBody,
    ValidationMapAppointment.ValidateCreateMapAppointment,
    MapAppointmentModule.createMapAppointment
  )
  // TODO :: Appointment

  // TODO :: Broken Car
  .post(
    "/PMS/broken-car/create-broken-car",
    DecodeMiddlewareModule.DecodeBody,
    ValidationBrokenCar.ValidateBrokenCar,
    BrokenCarModule.createBrokenCar
  )
  // TODO :: Broken Car

  // TODO :: Car Catalog
  .get("/PMS/catalog/get-catalog", GetCatalogModule.getCatalog)
  // TODO :: Car Catalog

  // TODO :: Check Profile
  .post(
    "/PMS/check-profile/check-profile",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCheckProfile.ValidateCheckProfile,
    CheckProfileModule.checkProfile
  )
  // TODO :: Check Profile

  // TODO :: Car Repair Status
  .post(
    "/PMS/car-repair-status/get-car-repair-status",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCarRepairStatus.ValidateCarRepairStatus,
    GetCarRepairStatusModule.getCarRepairStatus
  )
  .get(
    "/PMS/car-repair-status/update-payment-status/:partnerTxnUid",
    UpdatePaymentStatusModule.updatePaymentStatus
  )
  .post(
    "/PMS/car-repair-status/get-garage-service",
    DecodeMiddlewareModule.DecodeBody,
    ValidationGarageService.ValidateGarageService,
    GarageServiceModule.getGarageService
  )
  .post(
    "/PMS/car-repair-status/get-phone-by-car-reg",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCarRepairStatus.ValidateGetPhoneByCarReg,
    GetCarRepairStatusModule.getPhoneByCarReg
  )
  // TODO :: Car Repair Status

  // TODO :: Connect Social
  .post(
    "/PMS/connect-social/connect-social",
    DecodeMiddlewareModule.DecodeBody,
    ValidationConnectSocial.ValidateConnectSocial,
    ConnectSocialModule.connectSocial
  )
  // TODO :: Connect Social

  // TODO :: Event
  .get("/PMS/event/get-event", EventModule.getEvent)
  .post(
    "/PMS/event/register-event",
    DecodeMiddlewareModule.DecodeBody,
    ValidationEventRegister.ValidateEventRegister,
    EventRegisterModule.eventRegister
  )
  .post(
    "/PMS/event/check-register-event",
    DecodeMiddlewareModule.DecodeBody,
    ValidationEventRegister.ValidateEventRegister,
    EventRegisterModule.getEventRegisterById
  )
  // TODO :: Event

  // TODO :: Feedback
  .post(
    "/PMS/feedback/create-feedback",
    DecodeMiddlewareModule.DecodeBody,
    ValidationFeedBack.ValidateFeedBack,
    FeedbackModule.createFeedback
  )
  // TODO :: Feedback

  // TODO :: Home Service
  .post(
    "/PMS/home-service/create-home-service",
    DecodeMiddlewareModule.DecodeBody,
    ValidationHomeService.ValidateHomeService,
    HomeServiceModule.createHomeService
  )
  // TODO :: Home Service

  // TODO :: License Plate
  .get(
    "/PMS/license-plate/get-all-license-plate-by-phone/:phone",
    LicensePlateModule.getAllLicensePlateByPhone
  )
  .post(
    "/PMS/license-plate/create-license-plate",
    DecodeMiddlewareModule.DecodeBody,
    ValidationLicensePlate.ValidateCreateLicensePlate,
    LicensePlateModule.createLicensePlate
  )
  .put(
    "/PMS/license-plate/update-license-plate",
    DecodeMiddlewareModule.DecodeBody,
    ValidationLicensePlate.ValidateCreateLicensePlate,
    LicensePlateModule.updateLicensePlate
  )
  .delete(
    "/PMS/license-plate/delete-license-plate/:id",
    LicensePlateModule.deleteLicensePlate
  )
  // TODO :: License Plate

  // TODO :: Lifetime Appointment
  .post(
    "/PMS/lifetime-appointment/get-lifetime-appointment",
    DecodeMiddlewareModule.DecodeBody,
    ValidationLifetimeAppointment.ValidateLifetimeAppointment,
    LifetimeAppointmentModule.getLifetimeAppointment
  )
  .get(
    "/PMS/lifetime-appointment/send-noti-miles",
    LifetimeAppointmentMilesModule.sendNotiMiles
  )
  .get(
    "/PMS/lifetime-appointment/send-noti-rustproof",
    LifetimeAppointmentRustproofModule.sendNotiRustProof
  )
  .get(
    "/PMS/lifetime-appointment/send-noti-insurance",
    LifetimeAppointmentInsuranceModule.sendNotiInsurance
  )
  .get(
    "/PMS/lifetime-appointment/send-noti-PMG",
    LifetimeAppointmentPMGModule.sendNotiPMG
  )
  .post(
    "/PMS/lifetime-appointment/get-my-car-onday",
    DecodeMiddlewareModule.DecodeBody,
    ValidationLifetimeAppointment.CheckWho,
    LifetimeAppointmentModule.getDataOnday
  )
  // TODO :: Lifetime Appointment

  // TODO :: Login
  .post(
    "/PMS/login/login-tg",
    DecodeMiddlewareModule.DecodeBody,
    ValidateLoginTGModule.ValidateLoginTG,
    LoginTGModule.loginTG
  )
  // TODO :: Login

  // TODO :: MR
  .get("/PMS/MR/MRPOI", MRPOIModule.getMRPOI)
  .post("/PMS/MR/Check-MR-Code",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCheckMRCode.ValidateCheckMRCode,
    CheckMRCodeModule.checkMRCode
  )
  .post("/PMS/MR/Get-History-MR-Code",
    DecodeMiddlewareModule.DecodeBody,
    ValidationGetHistoryMRCode.ValidateGetHistoryMRCode,
     GetHistoryMRCodeModule.GetHistoryMRCode
  )
  // TODO :: MR

  // TODO :: Ref Code Mr 

  .post("/PMS/RefCode/update-ref-code-mr",
    DecodeMiddlewareModule.DecodeBody,
    ValidationUpdateRefCodeMr.ValidateUpdateRefCodeMr,
    UpdateRefCodeMrModule.updateRefCodeMr
  )

  .post("/PMS/RefCode/get-ref-code",
    DecodeMiddlewareModule.DecodeBody,
    ValidationGetRefCode.ValidateGetRefCode,
    GetRefCodeModule.getRefCode
  )
  // TODO :: Ref Code Mr 

  // TODO :: Map Marketing
  .post(
    "/PMS/activity/find-activity",
    DecodeMiddlewareModule.DecodeBody,
    ValidationSearchMapMarketing.ValidateSearchMapMarketing,
    SearchMapMarketingController.getMapMarketing
  )
  .post(
    "/PMS/activity/add-activity-data",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAddActivity.ValidateAddActivity,
    AddActivityDataModule.addActivityData
  )
  .put(
    "/PMS/activity/update-activity-data-pms",
    DecodeMiddlewareModule.DecodeBody,
    ValidationUpdateActivity.ValidateUpdateActivity,
    UpdateActivityDataModule.updateActivityData
  )
  .delete(
    "/PMS/activity/delete-activity-data/:running",
    DeleteActivityDataModule.deleteActivityData
  )
  .put(
    "/PMS/activity/update-activity-data",
    DecodeMiddlewareModule.DecodeBody,
    ValidationUpdateActivity.ValidateUpdateActivity,
    UpdateActivityDataModule.updateActivityData
  )
  // TODO :: Map Marketing

  // TODO :: New Car
  .post(
    "/PMS/new-car/get-new-car",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNewCar.ValidateNewCar,
    NewCarModule.getNewCar
  )
  .post(
    "/PMS/new-car/get-new-car-with-booking-no",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNewCar.ValidateNewCarWithBookingNo,
    NewCarModule.getNewCarWithBookingNo
  )
  // TODO :: New Car

  // TODO :: News
  .get("/PMS/news/get-news", GetNewsModule.getNews)
  .get("/PMS/news/get-news-all", GetNewsModule.getNewsAll)
  .get("/PMS/news/get-news-by-id/:id", GetNewsByIDModule.getNewsByID)
  .post(
    "/PMS/news/create-news",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCreateNews.ValidateCreateNews,
    createNewsModule.createNews
  )
  .post(
    "/PMS/news/update-news",
    DecodeMiddlewareModule.DecodeBody,
    ValidationUpdateNews.ValidateUpdateNews,
    updateNewsModule.updateNews
  )
  .post(
    "/PMS/news/delete-news",
    DecodeMiddlewareModule.DecodeBody,
    ValidationDeleteNews.ValidateDeleteNews,
    deleteNewsModule.deleteNews
  )
  // TODO :: News

  // TODO :: Notification
  .post(
    "/PMS/sendNotification/sendNotificationAppPMS",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    NotificationModule.sendNotificationAppPMS
  )
  .post(
    "/PMS/sendNotification/sendNotificationInAppPMS",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    NotificationModule.sendNotificationInAppPMS
  )
  .post(
    "/PMS/sendNotification/sendLineNoti",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    NotificationModule.sendLineNoti
  )
  ///part for e-sign
  .post(
    "/PMS/sendNotification/getNotifyByRunning",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    Notifycrud.getNotifyByRunning
  )
  .post(
    "/PMS/sendNotification/getNotiForRDS",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    Notifycrud.getNotify
  )
  .post(
    "/PMS/sendNotification/saveNotiForRDS",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    Notifycrud.saveNotify
  )
  .post(
    "/PMS/sendNotification/updateNotiForRDS",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    Notifycrud.updateNotify
  )
  .post(
    "/PMS/sendNotification/updateNotiForRDSFollowRunning",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    Notifycrud.updateNotifyFollowRunning
  )
  .post(
    "/PMS/sendNotification/deleteNotifyFollowRunning",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    Notifycrud.deleteNotifyFollowRunning
  )
  .post(
    "/PMS/sendNotification/deleteAllNotiEsign",
    DecodeMiddlewareModule.DecodeBody,
    ValidationNotification.ValidateSendNotification,
    Notifycrud.deleteNotifyAll
  )
  // TODO :: Notification

  // TODO :: OTP
  .post(
    "/PMS/OTP/sendOTP",
    DecodeMiddlewareModule.DecodeBody,
    ValidationOTP.sendOTP,
    SendOTPModule.sendOTP
  )
  .post(
    "/PMS/OTP/verifyOTP",
    DecodeMiddlewareModule.DecodeBody,
    ValidationOTP.verifyOTP,
    VerifyOTPModule.verifyOTP
  )
  // TODO :: OTP

  // TODO :: Promotion
  .get("/PMS/promotion/get-promotion", GetPromotionModule.getPromotion)
  .post(
    "/PMS/promotion/create-promotion",
    DecodeMiddlewareModule.DecodeBody,
    ValidationPronotion.ValidateCreatePronotion,
    createPromotionModule.createPromotion
  )
  .post(
    "/PMS/promotion/update-promotion",
    DecodeMiddlewareModule.DecodeBody,
    ValidationPronotion.ValidateUpdatePronotion,
    updatePromotionModule.updatePromotion
  )

  // .delete(
  //     '/PMS/promotion/delete-promotion/:promotion_id',
  //     deletePromotionModule.deletePromotion
  // )
  .post(
    "/PMS/promotion/delete-promotion",
    DecodeMiddlewareModule.DecodeBody,
    ValidationDeletePromotion.ValidateDeletePromotion,
    deletePromotionModule.deletePromotion
  )

  // TODO :: Promotion

  // TODO :: Register
  .post(
    "/PMS/register/register",
    DecodeMiddlewareModule.DecodeBody,
    ValidationRegister.ValidateRegister,
    registerModule.register
  )

  .post("/PMS/register/registerWithRefcode",
    DecodeMiddlewareModule.DecodeBody,
    ValidationRegister.ValidateRegister,
    registerModule.registerWithRefcode
  )
  .post(
    "/PMS/register/social",
    DecodeMiddlewareModule.DecodeBody,
    ValidationRegister.ValidateRegisterSocial,
    RegisterSocialModule.registerSocial
  )
  .post(
    "/PMS/Login/Login",
    DecodeMiddlewareModule.DecodeBody,
    ValidationRegister.ValidateRegister,
    registerModule.login
  )
  .post(
    "/PMS/register/register-tg",
    DecodeMiddlewareModule.DecodeBody,
    ValidateRegisterTGModule.ValidateRegisterTG,
    registerTGModule.registerTG
  )
  .post(
    "/PMS/register/update-id-tg",
    DecodeMiddlewareModule.DecodeBody,
    ValidateRegisterTGModule.ValidateUpdateIDTG,
    registerTGModule.updateIDTG
  )
  // TODO :: Register

  // TODO :: SaveActivity
  .post(
    "/PMS/activity/save-activity",
    DecodeMiddlewareModule.DecodeBody,
    ValidationSaveActivity.ValidateSaveActivity,
    SaveActivityModule.saveActivity
  )
  // TODO :: SaveActivity

  // TODO :: Update Agreement
  .post(
    "/PMS/member/update-agreement",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAcceptAgreement.ValidateAcceptAgreement,
    UpdateAgreementModule.updateAgreement
  )
  // TODO :: Update Agreement

  // TODO :: Estimate Color
  .post(
    "/PMS/estimate-color/get-estimate-color",
    DecodeMiddlewareModule.ByPassBody,
    ValidationEstimateColor.ValidateEstimateColor,
    EstimateColorModule.getEstimateColor
  )

  // PMS SERVICE
  // TODO :: Send Car
  .post(
    "/PMS/send-car/get-summary-sheet",
    DecodeMiddlewareModule.DecodeBody,
    ValidationPMSServiceSendCarPost.ValidateSendPMSServiceSendCarPost,
    PMSServiceSendCarPostModule.getSummarySheet
  )
  // TODO :: Send Car
  // TODO :: POI 
  .post( 
    "/PMS/poi/payLike",
    DecodeMiddlewareModule.DecodeBody,
    ValidationPOIPayLikepoint.ValidatePOIPayLikepoint,
    POIPaylikeModule.POIPaylike
  )
  // TODO :: POI
  .post(
    "/PMS/sendNotification/sendNotificationAppPMSByPass",
    DecodeMiddlewareModule.ByPassBody,
    ValidationNotification.ValidateSendNotification,
    NotificationModule.sendNotificationAppPMS
  )

  // PMS Prachakij
  // TODO :: GetAll Spare Part
  .get("/PMS/online-spare-parts/get-all-spare-parts",SparePartsGetModule.getAllSpareParts)
  // TODO :: Get Spare Part
  .post(
    "/PMS/online-spare-parts/get-spare-parts",
    DecodeMiddlewareModule.DecodeBody,
    ValidationSparePartsPost.ValidateSparePartsPost_,
    SparePartsPostModule.getSpareParts
  )
  
  .post(
    "/PMS/online-spare-parts/order-spare-parts",
    DecodeMiddlewareModule.DecodeBody,
    ValidationOrderSparePartsPost.ValidateOrderSparePartsPost_,
    OrderSparePartsPostModule.insertOrderSpareParts
  )
  
  .get("/PMS/online-spare-parts/order-spare-parts-get/:member_id",OrderSparePartsGetModule.getOrderDetails)
  
  .post(
    "/PMS/online-spare-parts/address-insert",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAddressSparePartsPost.validateAddressInsert,
    ShippingAddressesModule.insertShippingAddress
  )
   
  .post(
    "/PMS/online-spare-parts/address-update",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAddressSparePartsPost.validateAddressUpdate,
    ShippingAddressesModule.updateShippingAddress
  )
  
  .post(
    "/PMS/online-spare-parts/address-delete",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAddressSparePartsPost.validateAddressDelete,
    ShippingAddressesModule.deleteShippingAddress
  )
  
  .get("/PMS/online-spare-parts/address-getall/:member_id",ShippingAddressGetModule.getShippingAddresses)

  .post(
    "/PMS/online-spare-parts/address-set-default",
    DecodeMiddlewareModule.DecodeBody,
    ValidationAddressSparePartsPost.validateAddressSetDefault,
    ShippingAddressesModule.setDefaultShippingAddress
  )

  .post("/PMS/online-spare-parts/gen-qr-code",
    DecodeMiddlewareModule.DecodeBody,
    ValidationSyncPayment.genQrCode,
    SyncPaymentModule.genQrCode
  )

  .post("/PMS/online-spare-parts/gen-qr-code-test",
    DecodeMiddlewareModule.DecodeBody,
    SyncPaymentModule.qrCodePayment
  )

  .post("/PMS/online-spare-parts/update-payment-slip",
    DecodeMiddlewareModule.DecodeBody,
    ValidationSyncPayment.updatePaymentSlip,
    SyncPaymentModule.updatePaymentSlip
  )

  .post(
    "/PMS/psi/get-coupon-psi",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCouponPSI.ValidateCouponPSI,
    CouponPSIModule.getCouponPSI
  )
  .post(
    "/PMS/psi/get-car-owner",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCarOwner.ValidateCarOwner,
    CarOwnerModule.getCarOwner
  )
  .post(
    "/PMS/psi/get-car-picture",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCarOwner.ValidateCarPicture,
    CarOwnerModule.getCarPicture
  )

  .get("/PMS/online-spare-parts/update-payment-by-orderID/:order_id",SyncPaymentModule.checkStatusPaymentByOrderID)
  .get("/PMS/online-spare-parts/cancel-payment/:order_id",SyncPaymentModule.cancelPaymentByOrderID)

  .get("/PMS/online-spare-parts/check-status-payment",SyncPaymentModule.checkStatusPayment)

  .post("/PMS/recieve/encyptBody", EncyptIDModule.encyptBody)

  .post(
    "/PMS/couponRC/get-couponRC",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCouponRC.ValidateCouponRC,
    GetCouponRCModule.getCoupon
  )

  .post(
    "/PMS/couponRC/used-couponRC",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCouponRC.ValidateCouponRCUsed,
    InsertCouponModule.updateUseCoupon
  )
  .post(
    "/PMS/couponRC/insert-couponRC",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCouponRC.ValidateCouponRCInsert,
    InsertCouponModule.insertCoupon
  )
  .post(
    "/PMS/couponRC/claim-couponRC",
    DecodeMiddlewareModule.DecodeBody,
    ValidationCouponRC.ValidateCouponRCClaim,
    InsertCouponModule.updateClaimCoupon
  )
  .post(
    "/PMS/dashBoard/migrateDataSanToSA",
    DecodeMiddlewareModule.DecodeBody,
    ValidationMigrateJob.ValidateMigrateJobPhone,
    MigrateJobModule.MigrateJob
  )


// TODO :: RegisterSocial
// .get(
//     '/PMS/registerSocial/check-member-line/:lineID',
//     CheckMemberSocialModule.checkMemberSocialWithLine,
// )
// .get(
//     '/PMS/registerSocial/check-member-apple/:appleID',
//     CheckMemberSocialModule.checkMemberSocialWithApple,
// )
// .post(
//     '/PMS/registerSocial/create-registerSocial',
//     DecodeMiddlewareModule.DecodeBody,
//     ValidationRegisterSocial.ValidateRegisterSocial,
//     RegisterSocialModule.registerSocial,
// )
// TODO :: RegisterSocial

// TODO :: Member
// .get(
//     '/PMS/member/get-profile-and-mr/:id',
//     MemberModule.getProfileAndMR
// )
// .put(
//     '/member/update-profile',
//     DecodeMiddlewareModule.DecodeBody,
//     ValidationMember.ValidateUpdateProfile,
//     MemberModule.saveProfileByID
// )
// TODO :: Member

// ex url DELETE
// .delete(
//     '/license-plate/:id',
//     DecodeMiddlewareModule.DecodeBody,
//     ValidationLicensePlate.ValidateCreateLicensePlate,
//     LicensePlateModule.getAllLicensePlate
// )

////test P got
// router.post('/mysql/query', async (req) => {
//     const originHost = req.headers.get("host");
//     const uri = `https://${originHost}/mysql/query2`;

//     const bodyData = {
//         secret: "**********",
//     }

//     const authResponse = await pmsbinding2.fetch(
//         new Request(uri, {
//             method: "POST",
//             headers: { "content-type": "application/json" },
//             body: JSON.stringify(bodyData),
//         })
//     );

//         return new Response(JSON.stringify(await authResponse.json()), {
//             headers: { "content-type": "application/json" },
//         });
//     });
////test P got

// TODO :: Cron Job
addEventListener("scheduled", (e: ScheduledEvent) => {
  console.log("Running cron job at:", new Date().toISOString());
  e.waitUntil(LifetimeAppointmentMilesModule.sendNotiMiles());
  e.waitUntil(LifetimeAppointmentPMGModule.sendNotiPMG());
  e.waitUntil(LifetimeAppointmentInsuranceModule.sendNotiInsurance());
  e.waitUntil(LifetimeAppointmentRustproofModule.sendNotiRustProof());
});
// เอาไปใส้ใน wrangler.toml
//     [triggers]
// crons = ["*/5 * * * *"]
// TODO :: Cron Job

addEventListener("fetch", (event) => {
  const request = event.request;
  const url = new URL(request.url);
  // if (url.pathname.startsWith(PROXY_ENDPOINT)) {
  if (request.method === "OPTIONS") {
    // Handle CORS preflight requests
    event.respondWith(handleOptions(request));
  } else if (
    request.method === "GET" ||
    request.method === "HEAD" ||
    request.method === "PUT" ||
    request.method === "DELETE" ||
    request.method === "POST"
  ) {
    // Handle requests to the API server
    event.respondWith(router.handle(request));
  } else {
    event.respondWith(
      new Response(null, {
        status: 405,
        statusText: "Method Not Allowed",
      })
    );
  }
  // } else {
  //   // Serve demo page
  //   event.respondWith(rawHtmlResponse(DEMO_PAGE));
  // }
});

// addEventListener('fetch', event =>
// 		event.respondWith(router.handle(event.request))
// )
