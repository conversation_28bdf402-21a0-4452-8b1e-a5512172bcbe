const lineNotify = (token, message) => {
    console.log("waiting for send Line notify");
    const url = 'https://notify-api.line.me/api/notify';
    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Bearer ${token}`,
    };

    const payload = new URLSearchParams();
    payload.append('message', message);

    return new Promise(function (resolve, reject) {
        const notify = new Request(url, {
            method: "POST",
            headers: headers,
            body: payload.toString(),
        });
        (async () => {
            try {
                const result = await fetch(notify);
                resolve(result.json());
            } catch (e) {
                reject(e);
            }
        })();
    });
};

exports.lineNotify = lineNotify;
