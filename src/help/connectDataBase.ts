import { MysqlClient } from "./mysqlClient";

class connectdocker {
  static async connect(database, query, param) {
    try {
      console.log("connectdocker");

      const MySQLCon = new MysqlClient();

      if (param == undefined || param == null) {
        param = null;
      }

      var responseSuccessResult;
      if (ENV_DEPLOY !== "production") {

        responseSuccessResult = await MySQLCon.connect(database, query, param);
      } else {
        
        const originHost = MY_SQL_CLIENT_CON;

        responseSuccessResult = await SQLBIND.fetch(
          new Request(originHost, {
            body: JSON.stringify({
              database: database,
              query: query,
              values: param,
            }),
            headers: {
              "Content-Type": "application/json",
            },
            method: "POST",
          })
        );
        responseSuccessResult = await responseSuccessResult.json();
      }
      return responseSuccessResult;
    } catch (e) {
      console.log(e);
    }
  }
}

export { connectdocker };
