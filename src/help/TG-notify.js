function TGNotify(message, token) {
  const url = 'https://api.telegram.org/bot5059687928:AAHDlsBAFAnLTbciH8N_dUVwFDrBcf_KeZg/sendMessage'; // Replace with the actual Telegram API URL

  const newRequest = new Request(url, {
    body: JSON.stringify({
      text: message,
      chat_id: token,
      photo: '', // Remove this if it's unnecessary
    }),
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
    redirect: 'follow',
  });

  // Make the fetch request using .then() and .catch()
  return fetch(newRequest)
    .then(response => {
      return response; // Optionally, check for response.ok or status
    })
    .catch(error => {
      console.log(error);
      // return new Response(JSON.stringify({ error: error.message }), { status: 500 });
      return responseError(error.message, 500)
    });
}


exports.TGNotify = TGNotify;