// Use the `fetch` API provided by Cloudflare Workers, no need to import 'node-fetch' here.
class FetchRequest {
  public async post(url: string, body: any, auth: any): Promise<any> {
    try {
      const newRequest = new Request(url, {
        body: JSON.stringify(body),
        headers: {
          "Content-Type": "application/json",
          "Authorization": auth
        },
        method: "POST"
      });

      const response = await fetch(newRequest);
      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        const data = await response.text();
        throw new Error(data);
      }
    } catch (error) {
      throw new Error(error);
    }
  }
}

// Export the instance of the class directly, so it can be used in other files.
export const fetchRequestService = new FetchRequest();
