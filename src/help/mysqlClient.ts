import { Client } from "../driver/mysql";
import * as CryptoJ<PERSON> from "crypto-js";

export class MysqlClient {
  private options: any;

  constructor(options?: any) {
    this.options = options;
  }

  public async connect(
    database: string,
    query: string,
    param: any,
    table?: string,
    condition?: string
  ): Promise<any> {
    try {
      const mysql = new Client();
      const mysqlClient = await mysql.connect({
        username: DB_USER,
        db: database,
        hostname: TUNNEL_HOST || "https://tunnelaws.agilesoftgroup.com",
        password: DB_PASS,
        cfClientId: CF_CLIENT_ID || undefined,
        cfClientSecret: CF_CLIENT_SECRET || undefined,
      });

      const encryptedData = [];
      
      if (this.options) {
        // ฟังก์ชันเข้ารหัสข้อมูลด้วย Crypto.js
        console.log("Crypto.js");


        var key = CryptoJS.enc.Hex.parse(this.options.secret);
        var iv = CryptoJS.enc.Hex.parse(this.options.secret.substring(0, 16));
        function encryptData(data, secret) {
          if (typeof data === "string") {
            const ciphertext = CryptoJS.AES.encrypt(data, key, {
              iv: iv,
            }).toString();
            return ciphertext;
          }
          return data;
        }

        function decryptData(data, secret) {
          const isBase64 = (str) => {
            try {
              return btoa(atob(str)) == str;
            } catch (err) {
              return false;
            }
          };
          if (typeof data === "string" && isBase64(data)) {
            const bytes = CryptoJS.AES.decrypt(data, key, {
              iv: iv,
            });
            const originalText = bytes.toString(CryptoJS.enc.Utf8);
            return originalText;
          }
          return data;
        }

        function generateInsertQuery(data, table) {
          const fields = Object.keys(data);
          const values = Object.values(data);
          const placeholders = values.map(() => "?");

          const query = `INSERT INTO ${table} (${fields.join(
            ", "
          )}) VALUES (${placeholders.join(", ")})`;

          return query;
        }

        function generateUpdateQuery(table, data, condition) {
          const fields = Object.keys(data);
          const placeholders = fields.map((field) => `${field} = ?`);

          const query = `UPDATE ${table} SET ${placeholders.join(
            ", "
          )} WHERE ${condition}`;

          return query;
        }

        function generateParams(data) {
          return Object.values(data);
        }

        if (query.includes("SELECT") && table) {
          if (this.options.enccondition && param) {
            param = encryptData(param[0], this.options.secret);
          }
          const results = await mysqlClient.query(query, param);
          mysqlClient.close();
          // ตรวจสอบเงื่อนไขและเข้ารหัสข้อมูล
          results.forEach((row) => {
            this.options.entable.forEach((entable) => {
              if (entable.table === table) {
                entable.fields.forEach((field) => {
                  if (row.hasOwnProperty(field)) {
                    row[field] = decryptData(row[field], this.options.secret);
                  }
                });
              }
            });
            encryptedData.push(row);
          });
          return encryptedData;
        } else if (query.includes("INSERT") && table && Object.keys(param)) {
          // เข้ารหัสฟิลด์ Device_name ก่อนที่จะแทรกข้อมูลลงในฐานข้อมูล
          this.options.entable.forEach((entable) => {
            if (entable.table === table) {
              entable.fields.forEach((field) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = encryptData(param[field], this.options.secret);
                }
              });
            }
          });

          const query = generateInsertQuery(param, table);
          const params = generateParams(param);

          const result = await mysqlClient.query(query, params);
          mysqlClient.close();
          return result;
        } else if (query.includes("UPDATE") && table && condition) {
          this.options.entable.forEach((entable) => {
            if (entable.table === table) {
              entable.fields.forEach((field) => {
                if (param.hasOwnProperty(field)) {
                  param[field] = encryptData(param[field], this.options.secret);
                }
              });
            }
          });
          const query = generateUpdateQuery(table, param, condition);
          const params = generateParams(param);

          const result = await mysqlClient.query(query, params);
          mysqlClient.close();
          return result;
        }
      } else {
        console.log("else option null");
        
        const result = await mysqlClient.query(query, param);
        mysqlClient.close();
        return result;
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}