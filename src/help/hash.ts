import bcrypt from 'bcryptjs';

var hashPass = async (rawPass) => {
    try {
        console.log('waiting hash');
        const salt = await bcrypt.genSalt(10);
        const trimP = rawPass.trim();
        const passwordEncrypt = bcrypt.hashSync(trimP, salt);

        return passwordEncrypt
    } catch (e) {
        console.log(e);
        return rawPass;
    };
}

var compareSync = async (rawPass, dbPass) => {
    try {
        console.log('waiting compare');
        const passwordEncrypt = bcrypt.compareSync(rawPass, dbPass);
        console.log(passwordEncrypt);
        return passwordEncrypt
    } catch (e) {
        console.log(e);
        return false;
    };
}

export { hashPass, compareSync }