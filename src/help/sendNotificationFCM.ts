// ส่งข้อความไปยัง FCM โดยใช้ Access Token
/////////// new version ///////

import { log } from "driver/mysql";

const sendNotificationFCM = async (device_token, body_message, head_message, type, running,) => {
  try {
    const accessToken = await getAccessToken();

    const message = {
      message: {
        token: device_token, // ใส่ FCM device token ของอุปกรณ์
        notification: {
          title: head_message,
          body: body_message.trim().replace(/\n/g, ''),
        },
        data: {
          type: type,
          running: running
        },
      },
    };



    const response = await fetch("https://fcm.googleapis.com/v1/projects/mapp-pms/messages:send", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(message),
    });

    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.error || "Failed to send FCM message");
    }

    return data;
  } catch (e) {
    console.error("error: sendNotificationFCM", e);
    return false;
  }
};
/* ################################################## */
/* ######         ACCESS TOKEN SECTION       ######## */
/*                                                    */
/* -- GENERATE ACCESS TOKEN FOR FCM NOTIFICATION HTTPv1 FORMAT -- */
/*                                                    */
/* ################################################## */

// แปลง PEM private key ให้เป็น CryptoKey
async function importPrivateKey(pem: string): Promise<CryptoKey> {
  try {
    const pemHeader = "-----BEGIN PRIVATE KEY-----";
    const pemFooter = "-----END PRIVATE KEY-----";

    if (!pem.includes(pemHeader) || !pem.includes(pemFooter)) {
      throw new Error("Invalid PEM format");
    }

    const pemContents = pem
      .replace(pemHeader, "")
      .replace(pemFooter, "")
      .replace(/\s+/g, "");

    const binaryDerString = atob(pemContents);
    const binaryDer = new Uint8Array(binaryDerString.length);
    for (let i = 0; i < binaryDerString.length; i++) {
      binaryDer[i] = binaryDerString.charCodeAt(i);
    }

    const key = await crypto.subtle.importKey(
      "pkcs8",
      binaryDer.buffer,
      {
        name: "RSASSA-PKCS1-v1_5",
        hash: { name: "SHA-256" },
      },
      true,
      ["sign"]
    );

    // ตรวจสอบค่าคุณสมบัติของ CryptoKey ที่ได้
    // console.log('Key algorithm:', key.algorithm);
    // console.log('Key usages:', key.usages);
    // console.log('Key extractable:', key.extractable);
    // console.log('Key type:', key.type);

    return key;
  } catch (error) {
    console.error("Error importing private key:", error);
    throw new Error(`Failed to import private key: ${error.message}`);
  }
}

// สร้าง JWT โดยใช้ CryptoJS
async function createJWT(): Promise<string> {
  // CLIENT_EMAIL, PRIVATE_KEY
  const PRIVATE_KEY_BU = PRIVATE_KEY;
  const CLIENT_EMAIL_BU = CLIENT_EMAIL;

  const header = {
    alg: "RS256",
    typ: "JWT",
  };

  const iat = Math.floor(Date.now() / 1000); // เวลาปัจจุบันในรูปแบบ Unix timestamp
  const exp = iat + 3600; // 1 ชั่วโมงหลังจากนั้น

  const payload = {
    iss: CLIENT_EMAIL_BU, // Service account client email (เก็บเป็น secret)
    scope: "https://www.googleapis.com/auth/firebase.messaging", // Scope สำหรับ FCM
    aud: GOOGLE_OAUTH2_URL, // Audience (URL ที่ขอ token)
    iat: iat, // เวลาที่ออก JWT
    exp: exp, // เวลาหมดอายุ JWT
  };

  // เข้ารหัส header และ payload
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));

  // รวม header และ payload เข้าด้วยกัน
  const unsignedToken = `${encodedHeader}.${encodedPayload}`;

  const privateKey = await importPrivateKey(PRIVATE_KEY_BU);

  // เซ็นลายเซ็น JWT โดยใช้ RS256
  const signature = await crypto.subtle.sign(
    {
      name: "RSASSA-PKCS1-v1_5",
      hash: { name: "SHA-256" },
    },
    privateKey,
    new TextEncoder().encode(unsignedToken) // ใช้ TextEncoder เพื่อเข้ารหัสข้อมูลที่ต้องการเซ็น
  );

  // แปลง signature เป็น base64url
  const base64Signature = btoa(
    String.fromCharCode(...new Uint8Array(signature))
  )
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "");

  return `${unsignedToken}.${base64Signature}`;
}

function generateRandomId(length: number): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }
  return result;
}


// ขอ Access Token จาก Google OAuth 2.0 server
async function getAccessToken(): Promise<string> {
  const jwtToken = await createJWT();

  const response = await fetch(GOOGLE_OAUTH2_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
      assertion: jwtToken,
    }).toString(),
  });

  console.log("####### fetch GOOGLE_OAUTH2_URL #######");

  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to get access token");
  }
  console.log(`Generate AccessToken statusCode : ${response.status}`);

  return data.access_token;
}

async function createJWT2(): Promise<string> {
  const PRIVATE_KEY_BU = PRIVATE_KEY;
  const CLIENT_EMAIL_BU = CLIENT_EMAIL;

  const header = {
    alg: "RS256",
    typ: "JWT",
  };

  const iat = Math.floor(Date.now() / 1000); // Current time in Unix timestamp
  const exp = iat + 3600; // 1 hour expiration

  const payload = {
    iss: CLIENT_EMAIL_BU, // Service account client email
    scope: "https://www.googleapis.com/auth/datastore", // Firestore scope
    aud: GOOGLE_OAUTH2_URL, // Audience (token URL)
    iat: iat, // Issued at time
    exp: exp, // Expiration time
  };

  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  const unsignedToken = `${encodedHeader}.${encodedPayload}`;

  const privateKey = await importPrivateKey(PRIVATE_KEY_BU);

  const signature = await crypto.subtle.sign(
    {
      name: "RSASSA-PKCS1-v1_5",
      hash: { name: "SHA-256" },
    },
    privateKey,
    new TextEncoder().encode(unsignedToken)
  );

  const base64Signature = btoa(
    String.fromCharCode(...new Uint8Array(signature))
  )
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "");

  return `${unsignedToken}.${base64Signature}`;
}

async function getAccessTokenFirestore(): Promise<string> {
  const jwtToken = await createJWT2();

  const response = await fetch(GOOGLE_OAUTH2_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
      assertion: jwtToken,
    }).toString(),
  });

  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error || "Failed to get access token");
  }

  return data.access_token;
}


const getFirestore = async (userId: string) => {
  try {

    const accessToken = await getAccessTokenFirestore();

    console.log(accessToken);


    const response = await fetch(`https://firestore.googleapis.com/v1/projects/mapp-pms/databases/(default)/documents/${userId}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },

    });

    const data = await response.json();
    console.log(data);

    if (!response.ok) {
      throw new Error(data.error || "Failed to send FCM message");
    }

    return data;
  } catch (e) {
    console.error("error: sendNotificationFCM", e);
    return false;
  }
};

const addOrUpdateNotificationData = async (userId: string, notificationData: any) => {
  try {
    // create ต้องเป็น timestampValue ระวังดีๆ
    const accessToken = await getAccessTokenFirestore();
    console.log(accessToken);

    // Define the Firestore URL for the userId document
    const url = `https://firestore.googleapis.com/v1/projects/mapp-pms/databases/(default)/documents/mobileapp/notification/${userId}`;

    // Check if the userId document already exists
    const checkResponse = await fetch(url, {
      method: "GET", // Check if the userId document exists
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    // If the userId document does not exist, create it
    if (checkResponse.status === 404) {
      // Create the userId document with empty fields
      const initialData = notificationData;

      const createResponse = await fetch(url, {
        method: "PUT", // Create the userId document
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(initialData), // Send the initial data
      });

      if (!createResponse.ok) {
        const data = await createResponse.json();
        throw new Error(data.error || "Failed to create userId document");
      }
    }

    // Now that the userId document exists, add or update a notification with idToUse
    const idToUse = generateRandomId(20); // Generate a random ID if not provided
    const notificationUrl = `${url}/${idToUse}`; // Define the URL for the new document

    // Define the data to add (JSON object)
    const newData = notificationData;

    // Use PUT to create a new document under userId with the specified ID
    const response = await fetch(notificationUrl, {
      method: "PATCH", // Create the new notification document
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(newData), // Send the new document data
    });

    const data = await response.json();
    console.log(data);

    if (!response.ok) {
      throw new Error(data.error || "Failed to add notification data to Firestore");
    }

    return data; // Return the created notification data
  } catch (e) {
    console.error("error: addOrUpdateNotificationData", e);
    return false;
  }
};

const insertPhoneToFirestoreMenuMyCar = async (phone: string, collectionName: string, carList: { [key: string]: any }) => {
  try {
    const accessToken = await getAccessTokenFirestore();

    // Helper function to transform data into Firestore-compatible fields
    const transformToFirestoreFields = (data: any) => {
      const result: { [key: string]: any } = {};
      for (const key in data) {
        const value = data[key];
        if (typeof value === 'string') {
          result[key] = { stringValue: value };
        } else if (typeof value === 'number') {
          result[key] = { integerValue: value }; // Firestore uses integerValue for numbers
        } else if (typeof value === 'boolean') {
          result[key] = { booleanValue: value };
        } else if (Array.isArray(value)) {
          // Properly handle arrays
          result[key] = {
            arrayValue: {
              values: value.map((item) => {
                if (typeof item === 'object' && item !== null) {
                  return { mapValue: { fields: transformToFirestoreFields(item) } };
                } else {
                  // Wrap primitive values in Firestore-compatible format
                  return transformToFirestoreFields({ item }).item;
                }
              }),
            },
          };
        } else if (value instanceof Date) {
          result[key] = { timestampValue: value.toISOString() };
        } else if (typeof value === 'object' && value !== null) {
          result[key] = { mapValue: { fields: transformToFirestoreFields(value) } };
        } else {
          result[key] = { nullValue: null };
        }
      }
    
      return result;
    };

    const firestoreData = {
      fields: transformToFirestoreFields(carList),
    };

    const createPhoneDocResponse = await fetch(
      `https://firestore.googleapis.com/v1/projects/mapp-pms/databases/(default)/documents/${collectionName}/${phone}`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(firestoreData),
      }
    );

    const phoneDocData = await createPhoneDocResponse.json();
    if (!createPhoneDocResponse.ok) {
      console.error(phoneDocData.error || 'Failed to create phone document');
      return false;
    }

    return phoneDocData; // คืนค่าข้อมูลของ Document ที่สร้างขึ้น
  } catch (e) {
    console.error('Error inserting phone into Firestore:', e);
    return false;
  }
};

const updatePhoneInFirestoreMenuMyCar = async (phone: string, collectionName: string, documentID: string, updateData: { [key: string]: any }) => {
  try {
    const accessToken = await getAccessTokenFirestore();

    // Helper function to transform data into Firestore-compatible fields
    const transformToFirestoreFields = (data: any) => {
      const result: { [key: string]: any } = {};
      for (const key in data) {
        const value = data[key];
        if (typeof value === 'string') {
          result[key] = { stringValue: value };
        } else if (typeof value === 'number') {
          result[key] = { integerValue: value };
        } else if (typeof value === 'boolean') {
          result[key] = { booleanValue: value };
        } else if (Array.isArray(value)) {
          result[key] = {
            arrayValue: {
              values: value.map((item) => {
                if (typeof item === 'object' && item !== null) {
                  return { mapValue: { fields: transformToFirestoreFields(item) } };
                } else {
                  return transformToFirestoreFields({ item }).item;
                }
              }),
            },
          };
        } else if (value instanceof Date) {
          result[key] = { timestampValue: value.toISOString() };
        } else if (typeof value === 'object' && value !== null) {
          result[key] = { mapValue: { fields: transformToFirestoreFields(value) } };
        } else {
          result[key] = { nullValue: null };
        }
      }
      return result;
    };

    const firestoreData = {
      fields: transformToFirestoreFields(updateData),
    };

    const updateDocResponse = await fetch(
      `https://firestore.googleapis.com/v1/projects/mapp-pms/databases/(default)/documents/${collectionName}/${phone}/${documentID}`,
      {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(firestoreData),
      }
    );

    const updateResult = await updateDocResponse.json();
    if (!updateDocResponse.ok) {
      console.error(updateResult.error || 'Failed to update document');
      return false;
    }

    return updateResult; // คืนค่าข้อมูลของ Document ที่อัปเดต
  } catch (e) {
    console.error('Error updating phone in Firestore:', e);
    return false;
  }
};

const getPhoneDataFromFirestore = async (phone: string, collectionName: string) => {
  try {
    // ดึง Access Token
    const accessToken = await getAccessTokenFirestore();

    // สร้าง URL สำหรับ GET Request
    const url = `https://firestore.googleapis.com/v1/projects/mapp-pms/databases/(default)/documents/${collectionName}/${phone}`;
    console.log(url);
    
    // ส่ง GET Request
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
    });
    
    // if (response.status === 404) {
    //   console.warn(`Document with phone ${phone} not found.`);
    //   return false; // Document ไม่พบ
    // }

    const data = await response.json();
    
    if(data.documents.length > 0){
      console.log("out get");
      
      return data;  
    }else{
      return false;
    }
  } catch (e) {
    console.error('Error getting phone data from Firestore:', e);
    return false;
  }
};

// Helper function: แปลง Firestore data ให้เป็นรูปแบบ object ที่ใช้งานได้ง่าย
const parseFirestoreData = (fields: { [key: string]: any }) => {
  try {
    // ฟังก์ชันที่ใช้ในการแปลงค่าต่าง ๆ ใน Firestore ให้เป็นค่าที่เหมาะสม
    const parseValue = (value: any) => {
      if (value?.stringValue !== undefined) return value.stringValue;
      if (value?.integerValue !== undefined) return parseInt(value.integerValue, 10);
      if (value?.booleanValue !== undefined) return value.booleanValue;
      if (value?.timestampValue !== undefined) return new Date(value.timestampValue).toISOString(); // แปลง timestampValue เป็น ISO string
      if (value?.arrayValue !== undefined) {
        // ตรวจสอบ arrayValue และแปลงค่าของแต่ละสมาชิกใน array
        return value.arrayValue.values?.map((item: any) => parseFirestoreData(item.mapValue?.fields || item)) || [];
      }
      if (value?.mapValue !== undefined) {
        // ตรวจสอบ mapValue และแปลงฟิลด์ใน map
        return parseFirestoreData(value.mapValue.fields);
      }
      if (value?.nullValue !== undefined) return null;
      return value;
    };

    // ตรวจสอบ fields ว่าไม่ใช่ null หรือ undefined
    if (!fields || typeof fields !== 'object') {
      throw new Error("Invalid input: fields must be a non-null object");
    }

    // แปลง `fields` เป็น object
    return Object.entries(fields).reduce((acc, [key, value]) => {
      acc[key] = parseValue(value);
      return acc;
    }, {} as { [key: string]: any });
  } catch (e) {
    console.error('Error parsing Firestore data:', e);
  }
};


export { sendNotificationFCM, getFirestore, addOrUpdateNotificationData, insertPhoneToFirestoreMenuMyCar, updatePhoneInFirestoreMenuMyCar, parseFirestoreData, getPhoneDataFromFirestore };