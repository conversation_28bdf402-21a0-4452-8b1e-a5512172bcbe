const fetchData = (uri, bodyData, headers) => {
  return new Promise(function (resolve, reject) {
    if (headers) {
      headers = headers;
    } else {
      headers = {
        "Content-Type": "application/json",
      };
    }
    const newRequest = new Request(uri, {
      body: JSON.stringify(bodyData),
      headers: headers,
      method: "POST",
    });
    (async () => {
      try {
        const result = await fetch(newRequest);
        resolve(result.json());
      } catch (error) {
        reject(error);
      }
    })();
  });
};
exports.fetchData = fetchData;