const fetchDataOld = (device_token, head_message, body_message, type, running, auth) => {
    console.log("comming fetchData");
    return new Promise(function (resolve, reject) {
      (async () => {
        try {
          var bodyData = {
            "to": device_token,
            "notification": {
              "body": head_message,
              "title": body_message,
            },
            "data": {
              "type": type,
              "running": running
            },
          };
          console.log(bodyData);
          const url = "https://fcm.googleapis.com/fcm/send";
          console.log(auth);
  
          const newRequest = new Request(url, {
            body: JSON.stringify(bodyData),
            headers: {
              "Content-Type": "application/json",
              "Authorization": auth
            },
            method: "POST",
          });
  
          const response = await fetch(newRequest);
  
          const result = await response.json();
  
            console.log(result);
  
          return resolve(result);
        } catch (error) {
          console.log(error);
          return reject(error);
        }
      })();
    });
  };
  
  export { fetchDataOld };