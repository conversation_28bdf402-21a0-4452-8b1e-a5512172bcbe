interface Headers {
    [key: string]: string;
  }

  console.log('waitng fetchNormal');
  
  
  const fetchNormal = async (
    uri: string, 
    bodyData: any,
    headers?: Headers
  ): Promise<any> => {
  
    headers = headers ?? {
      "Content-Type": "application/json"
    };
  
    try {
      const response = await fetch(uri, {
        body: JSON.stringify(bodyData),
        headers: headers,
        method: "POST" 
      });
  
      return response.json();
  
    } catch (error) {
      throw error;
    }
  
  }
  
  export { fetchNormal };