import Joi from "joi";
import {Request} from "itty-router";
import {responseError} from "../../../help/response";

export class ValidateMapAppointment {
    
    public async ValidateCreateMapAppointment(req: any) {
        try { 
            const params = await req.data;

            let schema = Joi.object({
                    running: Joi.any(),
                    service_from: Joi.any(),
                    service_date_name: Joi.any(),
                    service_date_license: Joi.any(),
                    service_date_tel: Joi.any(),
                    row_spreadsheet: Joi.any(),
                    service_date_option: Joi.any(),
                    service_date_branch: Joi.any(),
                    service_date_latlng: Joi.any(),
                    service_date_location: Joi.any(),
                    service_date_date: Joi.any(),
                    service_date_time: Joi.any(),
                    service_car: Joi.any(),
                    activity_service: Joi.any(),
                    service_date_requirement: Joi.any(),
                    machine : Joi.any()
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationMapAppointment = new ValidateMapAppointment();