import { connectdocker } from "../../../help/connectDataBase";
import { ValidationAppointment } from "../validate/appointment-validate";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { NotificationModule } from "../../../modules/Notification/controller/notification-controller";
import { lineNotify } from "../../../help/line-notify";
import { fetchData } from "../../../help/fetch";
import { TGNotify } from "../../../help/TG-notify";

class MappAppointmentController {
    public async createMapAppointment(req: any) {
        try{
            let bodyData = await req.data;
            
            var formattedPhoneNumber = `${bodyData.service_date_tel.substr(0, 3)}-${bodyData.service_date_tel.substr(3)}`;
            var textItem = `คุณ ${bodyData.service_date_name} ${bodyData.service_date_option} ${bodyData.service_car}`;
            const map = bodyData.service_date_latlng != "" ? `https://www.google.com/maps/search/?api=1&query=${bodyData.service_date_latlng}` : "";
            
            let value = {
                service_date_license: bodyData.service_date_license ?? "",
                service_date_name: bodyData.service_date_name ?? "",
                service_date_tel: formattedPhoneNumber ?? "",
                need_date: bodyData.service_date_date ?? "",
                time_date_need: bodyData.service_date_time ?? "",
                branch_need: bodyData.service_date_branch ?? "",
                channel: bodyData.service_from ?? "Mapp",
                product: bodyData.service_date_option ?? "",
                Items: textItem ?? "",
                activity_service: bodyData.activity_service ?? "",
                service_date_latlng: bodyData.service_date_latlng ?? "",
                service_date_location: map ?? "",
                machine: bodyData.machine ?? ""
            }
            
            const res = await connectdocker.connect(
                DatabaseConfig.BCT_PMS_Appointment,
                "INSERT INTO Appointment (service_date_license, service_date_name, service_date_tel, need_date, time_date_need, branch_need, channel, product, Items, activity_service, service_date_latlng, service_date_location, machine) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [value.service_date_license, value.service_date_name, value.service_date_tel, value.need_date, value.time_date_need, value.branch_need, value.channel, value.product, value.Items, value.activity_service, value.service_date_latlng, value.service_date_location, value.machine]
            )
            
            const currentDate = new Date();
            const currentHour = currentDate.getHours();
            let noteText = "";

            if (currentHour >= 16) {
                // เวลาเกิน 5 โมงเย็นของประเทศไทย
                noteText = "ท่านได้ทำการจองคิวเรียบร้อยแล้ว พนักงานจะติดต่อกลับเพื่อคอนเฟิร์มวันและเวลาอีกครั้ง ภายในวันทำการ ไม่เกิน 09.30 น. ค่ะ";
            } else {
                // เวลาไม่เกิน 5 โมงเย็นของประเทศไทย
                noteText = "ท่านได้ทำการจองคิวเรียบร้อยแล้ว พนักงานจะติดต่อกลับเพื่อคอนเฟิร์มวันและเวลาอีกครั้ง ภายในวันและเวลาทำการค่ะ";
            }

            const service_date_date: string = bodyData.service_date_date;
            const dateObj: Date = new Date(service_date_date);
            const formatted_date: string = `${dateObj.getDate()}-${dateObj.getMonth() + 1}-${dateObj.getFullYear()}`;

            const reqNotificationInApp = {
                data: {
                    phone: bodyData.service_date_tel,
                    type: "notificationPic",
                    title: bodyData.service_date_option,
                    detail: `วันที่จองคิว : ${formatted_date}\nเวลา : ${bodyData.service_date_time} น.\nทะเบียนรถ : ${bodyData.service_date_license}\nสาขาที่เข้าใช้บริการ : ${bodyData.service_date_branch}`,
                    note: noteText
                }
            };

            let message = `\nมีลูกค้าใช้เมนู จองคิว/นัดหมาย จาก ${bodyData.service_from ?? "Mapp"} PMS\n\n"`;
                message += `ประเภทการจอง : ${bodyData.service_date_option}\n`;
                message += `ชื่อลูกค้า : ${bodyData.service_date_name}\n`
                message += `ทะเบียนรถ : ${bodyData.service_date_license}\n`;
                message += `ประเภทรถ : ${bodyData.service_car}\n`;
                message += `หมายเลขโทรศัพท์ : ${bodyData.service_date_tel}\n`;
                message += `ต้องการจองคิวที่สาขา : ${bodyData.service_date_branch}\n`;
                message += `วันที่ : ${formatted_date}\n`;
                message += `เวลา : ${bodyData.service_date_time}\n`;
                
            if (bodyData.service_date_option == "สานสัมพันธ์") {
                message += `แผนที่ : https://www.google.com/maps/search/?api=1&query=${bodyData.service_date_latlng}`
                var lineToken = "2gSImuFvff4zdJ2MeLBMnX2NAkCyBSWMbC6ud7aDeYS";
                const resLineNoti = await lineNotify(lineToken,message); ///ส่ง line
            } else if(bodyData.service_date_option == "ศูนย์บริการ") {
                var lineToken = "EuuxCJqqfwEjb0qrmOy2604yioEUGPh1TKjGSIRBLSN";
                var lineToken2 = "H1jPrf3AeQ9UvrzXx2f4IOi0HS5Q3SaikZKCy9omjQI";
                const resLineNoti = await lineNotify(lineToken,message); ///ส่ง line
                const resLineNoti2 = await lineNotify(lineToken2,message); ///ส่ง line
            } else if(bodyData.service_date_option == "TYP") {
                var TYPTOKEN = "EuuxCJqqfwEjb0qrmOy2604yioEUGPh1TKjGSIRBLSN";
                const resLineNoti = await lineNotify(TYPTOKEN,message); ///ส่ง line
            }
                
            const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp); ///ส่ง app
            
            return await responseSuccess(res, 200);
        }catch(e){
            console.log(e);
            return await responseError("ERROR :: Create Appointment", 407);
        }
    }
}

export let MapAppointmentModule = new MappAppointmentController();