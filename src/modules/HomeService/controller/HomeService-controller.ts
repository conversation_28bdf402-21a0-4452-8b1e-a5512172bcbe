import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { lineNotify } from "../../../help/line-notify";
import { responseError, responseSuccess } from "../../../help/response";
import { NotificationModule } from "../../../modules/Notification/controller/notification-controller";

class HomeServiceController {
    public async createHomeService(req: any) {
        try{
            let bodyData = await req.data;
            const map = `https://www.google.com/maps/search/?api=1&query=${bodyData.service_date_latlng}`;

            let value = {
                channel: bodyData.service_from ?? "Mapp",
                service_date_name: bodyData.service_date_name ?? "",
                service_date_license: bodyData.service_date_license ?? "",
                service_date_tel: bodyData.service_date_tel ?? "",
                service_date_latlng: bodyData.service_date_latlng ?? "",
                service_date_location: map ?? "",
                Items: bodyData.service_date_option ?? "",
                notes: bodyData.service_date_requirement ?? "",
                activity_service: bodyData.activity_service ?? "",
                product: bodyData.service_date_option ?? "",
            }
            const res = await connectdocker.connect(
                DatabaseConfig.BCT_PMS_Appointment,
                "INSERT INTO Appointment (service_date_license, service_date_name, service_date_tel, channel, Items, notes, service_date_latlng, service_date_location, activity_service) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [value.service_date_license, value.service_date_name, value.service_date_tel, value.channel, value.Items, value.notes, value.service_date_latlng, value.service_date_location, value.activity_service]
            )

            let message = `\nมีลูกค้าใช้เมนู จองคิว/นัดหมาย จาก ${bodyData.service_from ?? "Mapp"} PMS\n\n`;
                message += "ประเภทการจอง : บริการซ่อมถึงบ้าน\n";
                message += `ชื่อลูกค้า : ${bodyData.service_date_name}\n`
                message += `ทะเบียนรถ : ${bodyData.service_date_license}\n`;
                message += `หมายเลขโทรศัพท์ : ${bodyData.service_date_tel}\n\n`;
                message += `แผนที่ : https://www.google.com/maps/search/?api=1&query=${bodyData.service_date_latlng}`

            var lineToken = "2gSImuFvff4zdJ2MeLBMnX2NAkCyBSWMbC6ud7aDeYS";

            const resLineNoti = await lineNotify(lineToken,message); ///ส่ง line

            const currentDate = new Date();
            const currentHour = currentDate.getHours();
            let noteText = "";

            if (currentHour >= 16) {
                // เวลาเกิน 5 โมงเย็นของประเทศไทย
                noteText = "ท่านได้ทำการจองคิวบริการซ่อมถึงบ้านเรียบร้อยแล้ว พนักงานจะติดต่อกลับเพื่อคอนเฟิร์มวันและเวลาอีกครั้ง ภายในวันทำการ ไม่เกิน 09.30 น. ค่ะ";
            } else {
                // เวลาไม่เกิน 5 โมงเย็นของประเทศไทย
                noteText = "ท่านได้ทำการจองคิวบริการซ่อมถึงบ้านเรียบร้อยแล้ว พนักงานจะติดต่อกลับเพื่อคอนเฟิร์มวันและเวลาอีกครั้ง ภายในวันและเวลาทำการค่ะ";
            }
            
            const reqNotificationInApp = {
                data: {
                    phone: bodyData.service_date_tel,
                    type: "notificationPic",
                    title: bodyData.service_date_option,
                    detail: `\nทะเบียนรถ : ${bodyData.service_date_license}`,
                    note: noteText
                }
            };
            const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp); ///ส่ง app
            

            return await responseSuccess(res, 200);

        }catch(e){
            console.log(e);
            return await responseError("ERROR :: Create License Plate", 407);
        }
    }
}

export let HomeServiceModule = new HomeServiceController();
