import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateMigrateJob {

    public async ValidateMigrateJobPhone(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                    phone: Joi.string(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationMigrateJob = new ValidateMigrateJob();