import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";
import { getPhoneDataFromFirestore, parseFirestoreData } from "help/sendNotificationFCM";

class MigrateJobController {
  public async MigrateJob(req: any) {
    try {
      let bodyData = await req.data;

      console.log(bodyData);

      // Get data from AFSInvoiceHistoryReportSummary
      const resSummary = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT *
          FROM AFSInvoiceHistoryReportSummary
          WHERE Column24 = ?
          ORDER BY create_time DESC
          LIMIT 1`,
        [bodyData.phone]
      );

      // Get data from Firebase
      const resFirebase = await getPhoneDataFromFirestore(bodyData.phone, "myCar/EcnyzdWmCIxBrRnh5QTk");

      // Parse Firebase data
      let firebaseCarList = [];
      if (resFirebase && resFirebase.documents && resFirebase.documents.length > 0) {
        for (let i = 0; i < resFirebase.documents.length; i++) {
          const parsedData = parseFirestoreData(resFirebase.documents[i].fields);
          firebaseCarList.push(parsedData);
        }
      }

      console.log("firebaseCarList");
      console.log(firebaseCarList);
      

      let augmentedData = null;
      try {

        console.log("before compareAndAugmentData");
        const updatedData: any[] = [];

      // Check if we have data from both sources
      if (!resSummary || resSummary.length === 0) {
        console.log("No data found in resSummary");
        return updatedData;
      }
  
      if (!firebaseCarList || firebaseCarList.length === 0) {
        console.log("No data found in firebaseCarList");
        return updatedData;
      }
  
      const summaryData = resSummary[0]; // Get the first (most recent) record
  
      // Define field mappings
      const fieldMappings = [
        { firebase: "phone", summary: "Column24" },
        { firebase: "phone", summary: "Column26" },
        { firebase: "reg", summary: "Column27" },
        { firebase: "car_id", summary: "Column28" },
        { firebase: "machine_number", summary: "Column29" },
        { firebase: "miles[0].mileage", summary: "Column35" },
        { firebase: "miles[0].mileage", summary: "Column37" },
        { firebase: "miles[0].mileage_date", summary: "Column47" },
        { firebase: "miles[0].mileage_date", summary: "Column49" },
        { firebase: "miles[0].mileage_date", summary: "Column68" },
        { firebase: "miles[0].mileage_date", summary: "Column70" },
        { firebase: "miles[0].mileage_date", summary: "Column76" },
        { firebase: "miles[0].mileage_date", summary: "Column77" },
        { firebase: "miles[0].mileage_date", summary: "Column112" }
      ];
  
      // Compare each car in Firebase data with summary data
      for (const firebaseCar of firebaseCarList) {
        const updatedCar = { ...firebaseCar }; // Start with Firebase data
        let hasUpdates = false;
  
        // Check if this car matches the summary data (by phone or machine_number)
        const isMatchingCar =
          firebaseCar.phone === summaryData.Column24 ||
          firebaseCar.phone === summaryData.Column26 ||
          firebaseCar.machine_number === summaryData.Column29;
  
        if (isMatchingCar) {
          console.log(`Found matching car for phone: ${firebaseCar.phone}, machine: ${firebaseCar.machine_number}`);
  
          // Compare and update fields based on mappings
          for (const mapping of fieldMappings) {
            const summaryValue = summaryData[mapping.summary];
  
            if (summaryValue !== null && summaryValue !== undefined && summaryValue !== '') {
              const firebaseValue = mapping.firebase.split('.').reduce((current, key) => {
                if (key.includes('[') && key.includes(']')) {
                  const arrayKey = key.substring(0, key.indexOf('['));
                  const index = parseInt(key.substring(key.indexOf('[') + 1, key.indexOf(']')));
                  return current?.[arrayKey]?.[index];
                }
                return current?.[key];
              }, firebaseCar);
              
  
              // Check if summary data is newer or different
              if (!isNaN(summaryValue.getTime()) && !isNaN(firebaseValue.getTime())) {
                // this.setNestedValue(updatedCar, mapping.firebase, summaryValue);
                hasUpdates = true;
                console.log(`Updated ${mapping.firebase} from ${firebaseValue} to ${summaryValue}`);
              }
            }
          }
  
          augmentedData = updatedCar;
          // Add timestamp of when this update was processed
          if (hasUpdates) {
            updatedCar.lastUpdated = new Date().toISOString();
            updatedCar.updateSource = 'AFSInvoiceHistoryReportSummary';
            updatedData.push(updatedCar);
          }
        }
      }

        console.log("after compareAndAugmentData");
      } catch (err) {
        console.error("Error in compareAndAugmentData call:", err);
      }

      return await responseSuccess({
        augmentedData: augmentedData
      }, 200);
    } catch (error) {
      return await responseError(error, 500);
    }
  }
}

export let MigrateJobModule = new MigrateJobController();
