import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { fetchData } from "help/fetch";
import { responseError, responseSuccess } from "help/response";
import { getPhoneDataFromFirestore, parseFirestoreData } from "help/sendNotificationFCM";
import { LifetimeAppointmentModule } from "modules/LifetimeAppointment/controller/lifetime-appointment-controller";

class MigrateJobController {
  public async MigrateJob(req: any) {
    try {
      let bodyData = await req.data;

      console.log(bodyData);
    
      const resSummary = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT *
          FROM AFSInvoiceHistoryReportSummary
          WHERE Column24 = ?
          ORDER BY create_time DESC
          LIMIT 1`,
        [bodyData.phone]
    ); 

    const resFirebase = await getPhoneDataFromFirestore(bodyData.phone, "myCar/EcnyzdWmCIxBrRnh5QTk");

    var dataOut = [];
        for (var i = 0; i < resFirebase.documents.length; i++) {
          const parsedData = parseFirestoreData(resFirebase.documents[i].fields);
          dataOut.push(parsedData);
        }

      return await responseSuccess({"data1": resSummary, "data2": dataOut}, 200);
    } catch (error) {
      return await responseError(error);
    }
  }
}

export let MigrateJobModule = new MigrateJobController();
