import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { fetchData } from "help/fetch";
import { responseError, responseSuccess } from "help/response";
import { LifetimeAppointmentModule } from "modules/LifetimeAppointment/controller/lifetime-appointment-controller";

class MigrateJobController {
  public async MigrateJob(req: any) {
    try {
      let bodyData = await req.data;

      console.log(bodyData);
    
      const data = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT *
          FROM AFSInvoiceHistoryReportSummary
          WHERE Column24 = ?
          ORDER BY create_time DESC
          LIMIT 1`,
        [bodyData.phone]
    ); 

    const data2 = await LifetimeAppointmentModule.getLifetimeAppointment({"phone": bodyData.phone});
      
      return await responseSuccess({data1: data, data2: data2}, 200);
    } catch (error) {
      return await responseError(error);
    }
  }
}

export let MigrateJobModule = new MigrateJobController();
