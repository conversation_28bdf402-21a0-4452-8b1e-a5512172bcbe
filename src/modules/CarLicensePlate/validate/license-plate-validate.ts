import Joi from "joi";
import {Request} from "itty-router";
import {responseError} from "../../../help/response";


export class ValidateLicensePlate {

    public async ValidateGetLicensePlate(req: any) {
        try {
            const paramsGet = await req.data;
            
            let schemaGet = Joi.object({
                phone: Joi.required()
            });

            const {error} = await schemaGet.validate(paramsGet);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async ValidateCreateLicensePlate(req: any) {
        try {

            const params = await req.data;
            console.log(params);
            
            let schema = Joi.object({
                car_user_phone: Joi.any(),
                create_user: Joi.any(),
                car_character: Joi.any(),
                car_number: Joi.string().required(),
                car_province: Joi.string().required(),
                car_license: Joi.string().required(),
                car_engine_no: Joi.any(),
                car_type: Joi.any(),
                running: Joi.any(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationLicensePlate = new ValidateLicensePlate();

