import { Request } from "itty-router";
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { LicensePlateType } from "../type/license-plate-type";

class LicensePlateController {

  public async getAllLicensePlateByPhone(req: any) {
    try{
      console.log("getAllLicensePlateByPhone");
      const params = await req.params;
      console.log(params);
      
      var res = await connectdocker.connect(
      DatabaseConfig.webPKG,
      `SELECT running, car_user_phone, car_character, car_number, car_province, car_license, car_engine_no FROM car WHERE car_user_phone = ?`,
      [params.phone.trim()]
    ) as LicensePlateType[]
    console.log(typeof res);
    
    return responseSuccess(res, 200);
    }catch (e){
      console.log(e);
      return await responseError("ERROR :: Get All License Plate", 407);
    }
  }

  public async createLicensePlate(req: any) {
    try{
      let bodyData = await req.data;
      let value = {
        car_user_phone: bodyData.car_user_phone,
        create_user: bodyData.create_user,
        car_character: bodyData.car_character,
        car_number: bodyData.car_number,
        car_province: bodyData.car_province,
        car_license: bodyData.car_license,
        car_engine_no: bodyData.car_engine_no,
        car_type: bodyData.car_type
      }

      let res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        // `INSERT INTO car SET ?`,
        "INSERT INTO car (car_user_phone, create_user, car_character, car_number, car_province, car_license, car_engine_no, car_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        [value.car_user_phone, value.create_user, value.car_character, value.car_number, value.car_province, value.car_license, value.car_engine_no, value.car_type]
      )
      return await responseSuccess(res, 200);
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: Create License Plate", 407);
    }
  }

  public async updateLicensePlate(req: any){
    try{
      let bodyData = await req.data;
      let value = [
        bodyData.car_character, 
        bodyData.car_number,
        bodyData.car_province,
        bodyData.car_license
      ]
      let res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `UPDATE car SET car_character = ?, car_number = ?, car_province = ?, car_license = ? WHERE running = ${bodyData.running}`,
        value
      )
      return await responseSuccess(res, 200);
    }catch (e){
      console.log(e);
      return await responseError("ERROR :: Update License Plate", 407);
    }
  }

  public async deleteLicensePlate(req: any) {
    try{
      const params = await req.params;
      const res = await connectdocker.connect(
      DatabaseConfig.webPKG,
      `DELETE FROM car WHERE running = ?`,
      [params.id]
    )
    return responseSuccess(res, 200);
    }catch (e){
      return await responseError("ERROR :: Delete License Plate", 407);
    }
  }
  
}

export let LicensePlateModule = new LicensePlateController();