import { responseError } from "help/response";
import Joi from "joi";

export class ValidateAddActivity {

    public async ValidateAddActivity(req: any){
        try {
            const params = await req.data;
            let schema = Joi.object({
                running: Joi.any(),
                first_lastName: Joi.any(),
                S_ID: Joi.any(),
                regis: Joi.any(),
                engine: Joi.any(),
                firstName: Joi.any(),
                lastName: Joi.any(),
                mobilePhone: Joi.any(),
                contact_status: Joi.any(),
                pu_id: Joi.any(),
                title: Joi.any(),
                event_name: Joi.any(),
                pursuit_channel: Joi.any(),
                data_name: Joi.any(),
                note1: Joi.any(),
                note2: Joi.any(),
                note3: Joi.any(),
                causes_no_contact: Joi.any(),
                user_insert: Joi.any(),
                address: Joi.any(),
                tumbol: Joi.any(),
                amphur: Joi.any(),
                province: Joi.any(),
                zipcode: Joi.any(),
                mirai_note21: Joi.any(),
                mirai_note18: Joi.any(),
                mirai_note19: Joi.any(),
                p_reports: Joi.any(),
                appointment_status: Joi.any(),
                interested_status: Joi.any(),
                details: Joi.any(),
                due_date: Joi.any(),
                date_contact: Joi.any(),
            });

            const {error} = await schema.validate(params);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationAddActivity = new ValidateAddActivity();