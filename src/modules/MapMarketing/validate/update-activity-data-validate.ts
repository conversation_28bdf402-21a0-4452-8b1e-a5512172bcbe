import { responseError } from "help/response";
import <PERSON><PERSON> from "joi";

export class ValidateUpdateActivity {
    public async ValidateUpdateActivity(req: any){
        try{
            const params = await req.data;
            let schema = Joi.object({
                running: Joi.any(),
                update_user: Joi.any(),
                contact_status: Joi.any(),
                mirai_note17: Joi.any(),
                mirai_note18: Joi.any(),
            });

            const {error} = await schema.validate(params);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationUpdateActivity = new ValidateUpdateActivity();