import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class UpdateActivityDataController {
    public async updateActivityData(req: any) {
        try {
            const bodyParams = await req.data;
            const value = {
                running: bodyParams.running ?? "",
                contact_status: bodyParams.contact_status ?? "",
                update_user: bodyParams.update_user ?? "",
                mirai_note17: bodyParams.mirai_note17 ?? "",
                mirai_note18: bodyParams.mirai_note18 ?? "",
            }
            const res = await connectdocker.connect(
                DatabaseConfig.DATACENTER,
                `UPDATE activity_data_PMS
                 SET contact_status = ?,
                     update_user    = ?,
                     mirai_note17   = ?,
                     mirai_note18   = ?
                     WHERE running = ?`,
                [value.contact_status, value.update_user, value.mirai_note17, value.mirai_note18, value.running]
            )

            if (res.affectedRows > 0) {
                return await responseSuccess(res, 200);
            } else {
                return await responseError("ERROR :: Update Appointment un success", 407);
            }
        
        } catch(e){
            console.log(e);
            return await responseError("ERROR :: Update Appointment", 407);
        }
    }
}

export let UpdateActivityDataModule = new UpdateActivityDataController();