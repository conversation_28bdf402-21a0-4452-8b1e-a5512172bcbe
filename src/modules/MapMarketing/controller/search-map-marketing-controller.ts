import { log } from "console";
import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class SearchMapMarketing {
    public async getMapMarketing(req: any){
        try {
            const bodyData = await req.data;
            const take = (50 * Number(bodyData.page - 1));
            const year = new Date().getFullYear().toString().substr(-2);
            const month = new Date().getMonth() + 1;
            const result = await connectdocker.connect(
                DatabaseConfig.DATACENTER,
                `
                SELECT DISTINCT
                    tendance.link_group AS link_line,
                    activity_pms.running,
                    activity_pms.first_lastName,
                    activity_pms.mobilePhone,
                    activity_pms.regis,
                    activity_pms.engine,
                    activity_pms.pu_id,
                    activity_pms.address,
                    activity_pms.tumbol,
                    activity_pms.district,
                    activity_pms.province,
                    activity_pms.zip,
                    activity_pms.status,
                    activity_pms.employee,
                    activity_pms.create_time,
                    activity_pms.mirai_note19 AS trackingDate,
                    activity_pms.mirai_note16 AS mileage,
                    activity_pms.mirai_note15 AS coupon,
                    activity_pms.mirai_note34 AS excellency,
                    activity_pms.title AS service,
                    activity_pms.token AS token_line,
                    activity_pms.guarantee
                FROM activity_data_PMS AS activity_pms
                INNER JOIN BCT_Feelwell.tendance_5 AS tendance ON activity_pms.engine = tendance.machine
                WHERE activity_pms.employee =  ?
                AND activity_pms.BU = 'PMS'
                AND (activity_pms.pu_id LIKE ? OR activity_pms.pu_id LIKE ? OR activity_pms.pu_id LIKE ?)
                AND activity_pms.title != 'โครงการพิเศษ'
                ORDER BY activity_pms.running DESC
                LIMIT 50 OFFSET ?
                `,
                [bodyData.employee, `%PROPMS007-${year}${month}%`, `%PROPMS002-${year}${month}%`, `%PROPCC003-${year}${month}%`, take]
            );
          
            const resultTotalRows = await connectdocker.connect(
                DatabaseConfig.DATACENTER, `
                SELECT COUNT(*) AS total_count
                FROM activity_data_PMS AS activity_pms
                INNER JOIN BCT_Feelwell.tendance_5 AS tendance ON activity_pms.engine = tendance.machine
                WHERE activity_pms.employee =  ?
                  AND activity_pms.BU = 'PMS'
                  AND (activity_pms.pu_id LIKE ? OR activity_pms.pu_id LIKE ? OR activity_pms.pu_id LIKE ?)
                  AND activity_pms.title != 'โครงการพิเศษ';
  `,
  [bodyData.employee,  `%PROPMS007-${year}${month}%`, `%PROPMS002-${year}${month}%`, `%PROPCC003-${year}${month}%`] );
            
            const resultDate = {
                "total_rows": resultTotalRows[0].total_count,
                "data": result
            }
            
            
            if (result.length === 0) {
                return await responseError("MapMarketing not found", 404);
            }

            return responseSuccess(resultDate, 200);
            
        } catch (e) {
            console.log(e);
            return await responseError("ERROR :: getMapMarketing ", 407);

        }
    }
}

export let SearchMapMarketingController = new SearchMapMarketing();