import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class AddActivityDataController {
    public async addActivityData(req: any) {
        try {
            const bodyData = await req.data;
            const value = {
                running: bodyData.running ?? "",
                first_lastName: bodyData.first_lastName ?? "",
                S_ID: bodyData.S_ID ?? "",
                regis: bodyData.regis ?? "",
                engine: bodyData.engine ?? "",
                firstName: bodyData.firstName ?? "",
                lastName: bodyData.lastName ?? "",
                mobilePhone: bodyData.mobilePhone ?? "",
                contact_status: bodyData.contact_status ?? "",
                pu_id: bodyData.pu_id ?? "",
                title: bodyData.title ?? "",
                event_name: bodyData.event_name ?? "",
                pursuit_channel: bodyData.pursuit_channel ?? "",
                data_name: bodyData.data_name ?? "",
                note1: bodyData.note1 ?? "",
                note2: bodyData.note2 ?? "",
                note3: bodyData.note3 ?? "",
                causes_no_contact: bodyData.causes_no_contact ?? "",
                user_insert: bodyData.user_insert ?? "",
                address: bodyData.address ?? "",
                tumbol: bodyData.tumbol ?? "",
                amphur: bodyData.amphur ?? "",
                province: bodyData.province ?? "",
                zipcode: bodyData.zipcode ?? "",
                mirai_note21: bodyData.mirai_note21 ?? "",
                mirai_note18: bodyData.mirai_note18 ?? "",
                mirai_note19: bodyData.mirai_note19 ?? "",
                p_reports: bodyData.p_reports ?? "",
                appointment_status: bodyData.appointment_status ?? "",
                interested_status: bodyData.interested_status ?? "",
                details: bodyData.details ?? "",
                due_date: bodyData.due_date ?? "",
                date_contact: bodyData.date_contact ?? "",
            }
            const res = await connectdocker.connect(
                DatabaseConfig.DATACENTER,
                `INSERT INTO activity 
                (running, first_lastName, S_ID, regis, engine, firstName, lastName, mobilePhone, contact_status, pu_id, title, event_name, pursuit_channel, data_name, note1, note2, note3, causes_no_contact, user_insert, address, tumbol, amphur, province, zipcode, mirai_note21, mirai_note18, mirai_note19, p_reports, appointment_status, interested_status, details, due_date, date_contact)
              VALUES
                (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)  
              ON DUPLICATE KEY UPDATE
                first_lastName = VALUES(first_lastName),
                S_ID = VALUES(S_ID),
                regis = VALUES(regis),
                engine = VALUES(engine),
                firstName = VALUES(firstName),
                lastName = VALUES(lastName),
                mobilePhone = VALUES(mobilePhone),
                contact_status = VALUES(contact_status),
                pu_id = VALUES(pu_id),
                title = VALUES(title),
                event_name = VALUES(event_name),
                pursuit_channel = VALUES(pursuit_channel),
                data_name = VALUES(data_name),
                note1 = VALUES(note1),
                note2 = VALUES(note2),
                note3 = VALUES(note3),
                causes_no_contact = VALUES(causes_no_contact),
                user_insert = VALUES(user_insert), 
                address = VALUES(address),
                tumbol = VALUES(tumbol),
                amphur = VALUES(amphur),
                province = VALUES(province),
                zipcode = VALUES(zipcode),
                mirai_note21 = VALUES(mirai_note21),
                mirai_note18 = VALUES(mirai_note18),
                mirai_note19 = VALUES(mirai_note19),
                p_reports = VALUES(p_reports),
                appointment_status = VALUES(appointment_status),
                interested_status = VALUES(interested_status),
                details = VALUES(details),
                due_date = VALUES(due_date),
                date_contact = VALUES(date_contact)`,
                [value.running, value.first_lastName, value.S_ID, value.regis, value.engine, value.firstName, value.lastName, value.mobilePhone, value.contact_status, value.pu_id, value.title, value.event_name, value.pursuit_channel, value.data_name, value.note1, value.note2, value.note3, value.causes_no_contact, value.user_insert, value.address, value.tumbol, value.amphur, value.province, value.zipcode, value.mirai_note21, value.mirai_note18, value.mirai_note19, value.p_reports, value.appointment_status, value.interested_status, value.details, value.due_date, value.date_contact]
            )

            if (res["affectedRows"] === 0) {
                return await responseError("NOT ERROR :: BUT NOT UPDATE", 406);
            }

            if (res["affectedRows"] === 1) {
                return await responseSuccess({
                    status: "INSERT",
                    res
                }, 200);
            }

            if (res["affectedRows"] === 2) {
                return await responseSuccess({
                    status: "UPDATE",
                    res
                }, 200);
            }

        } catch (error) {
            console.log(error);
            return await responseError("ERROR :: Create Appointment", 407);
        }
    }
}

export let AddActivityDataModule = new AddActivityDataController();