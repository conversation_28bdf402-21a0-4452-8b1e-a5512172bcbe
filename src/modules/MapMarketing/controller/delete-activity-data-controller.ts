import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class DeleteActivityDataController {
    public async deleteActivityData(req: any) {
        try {
            const bodyParams = await req.params;
            const value = {
                running: bodyParams.running ?? ""
            }

            const res = await connectdocker.connect(
                DatabaseConfig.DATACENTER,
                `DELETE FROM activity_data_PMS WHERE running = ?`,
                [value.running]
            )

            return await responseSuccess(res, 200);
        
        } catch(e){
            console.log(e);
            return await responseError("ERROR :: Delete Appointment", 407);
        }
    }
}

export let DeleteActivityDataModule = new DeleteActivityDataController();