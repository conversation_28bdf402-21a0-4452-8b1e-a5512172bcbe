import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { fetchRequestService } from "help/fetchButTS";

class CouponPSIController {
  // Single function to get PSI coupon
  public async getCouponPSI(req: any, env: any) {
    try {
      const bodyData = req.data;

      // เตรียมรูปแบบเบอร์โทรทั้งที่มีขีดและไม่มีขีด
      const formattedPhone = bodyData.phone.replace(/-/g, ''); // เบอร์โทรแบบไม่มีขีด
      const originalPhone = bodyData.phone; // เบอร์โทรแบบต้นฉบับ

      // SQL query for sa_image
      const sqlCar = `
        SELECT MAX(running) AS running, name, phone, reg, engin 
        FROM sa_image 
        WHERE REPLACE(phone, '-', '') LIKE ? OR phone LIKE ?
        GROUP BY engin
      `;
      const resCar = await connectdocker.connect(DatabaseConfig.MIRAI, sqlCar, [
        formattedPhone,
        originalPhone,
      ]);

      // SQL query for BCT_CONTRACT
      const sqlNew = `
        SELECT engineCode 
        FROM BCT_CONTRACT 
        WHERE REPLACE(centerPhone, '-', '') LIKE ? OR centerPhone LIKE ?
        GROUP BY engineCode
      `;
      const resNew = await connectdocker.connect(DatabaseConfig.BCT_AMS1, sqlNew, [
        formattedPhone,
        originalPhone,
      ]);

      // Combine results based on matching engin and engineCode
      const engineList = [
        ...new Set([
          ...resCar.map(car => car.engin), // ดึงค่าจาก resCar
          ...resNew.map(newEntry => newEntry.engineCode), // ดึงค่าจาก resNew
        ]),
      ];

      try {

        var additionalApiResponse = await fetchRequestService.post(
          `https://devdev.prachakij.com/PMS/sa/psi/ajax/coupon_multiple.php`,
          {
            "engine_code_array": engineList
           },
          {
            "Content-Type": "application/json",
          }
        );

        return await responseSuccess(additionalApiResponse,200);
      } catch (error) {
        console.error("Error processing car:", error);
        return await responseError(`ERROR :: processing car => ${error}`, 407);
      }  
    } catch (error) {
      console.error(`ERROR :: getCouponPSI => ${error}`);
      return await responseError(`ERROR :: getCouponPSI => ${error}`, 407);
    }
  }
}

export const CouponPSIModule = new CouponPSIController();