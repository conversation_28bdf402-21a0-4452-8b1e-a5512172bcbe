import { getPhoneDataFromFirestore, parseFirestoreData, insertPhoneToFirestoreMenuMyCar, updatePhoneInFirestoreMenuMyCar } from "../../../help/sendNotificationFCM";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class CarOwnerController {
    // Single function to get PSI coupon
    public async getCarOwner(req: any, env: any) {
      try {
          const bodyData = req.data;
          const carList: any[] = [];
  
          var resFirebase = await getPhoneDataFromFirestore(bodyData.phone, "myCarOwner/uxi3UzQy43KPi5QC8mN2");
          
          // เตรียมรูปแบบเบอร์โทรทั้งที่มีขีดและไม่มีขีด
          const formattedPhone = bodyData.phone.replace(/-/g, ''); // เบอร์โทรแบบไม่มีขีด
          const originalPhone = bodyData.phone; // เบอร์โทรแบบต้นฉบับ
  
          const sqlCar = `SELECT 
              si.running, 
              si.name, 
              si.phone, 
              si.reg, 
              si.engin, 
              si.create_time
          FROM 
              sa_image si
          INNER JOIN (
              SELECT 
                  engin, 
                  MAX(create_time) AS max_create_time
              FROM 
                  sa_image
              WHERE 
                  REPLACE(phone, '-', '') LIKE ? OR phone LIKE ?
              GROUP BY 
                  engin
          ) max_results
          ON 
              si.engin = max_results.engin 
              AND si.create_time = max_results.max_create_time;
          `;
          const resCar = await connectdocker.connect(DatabaseConfig.MIRAI, sqlCar, [
              formattedPhone,
              originalPhone,
          ]);
  
          console.log("resCar");
          console.log(resCar);
  
          if (resFirebase != false && resFirebase.documents.length === resCar.length) {
              // แปลงข้อมูล Firestore เป็นรูปแบบที่ใช้งานง่าย
              var dataOut = [];
              for (var i = 0; i < resFirebase.documents.length; i++) {
                  const parsedData = parseFirestoreData(resFirebase.documents[i].fields);
                  dataOut.push(parsedData);
              }
  
              // ตรวจสอบ create_time กับ dataUpdate สำหรับทุกคู่
              let allFirebaseNewer = true;
              for (let i = 0; i < resCar.length; i++) {
                  const createTime = resCar[i]?.create_time ? new Date(resCar[i].create_time) : new Date(0);
                  const dataUpdate = dataOut[i]?.dataUpdate ? new Date(dataOut[i].dataUpdate) : new Date(0);
  
                  console.log(`Comparing index ${i}: createTime=${createTime}, dataUpdate=${dataUpdate}`);
                  console.log(`createTime < dataUpdate: ${createTime < dataUpdate}`);
  
                  if (createTime >= dataUpdate) {
                      allFirebaseNewer = false;
                      break; // ถ้ามีคู่ใดคู่หนึ่งที่ create_time >= dataUpdate ให้ออกจาก loop
                  }
              }
  
              // ถ้าทุกคู่มี create_time < dataUpdate ให้คืนข้อมูลจาก Firestore
              if (allFirebaseNewer) {
                  console.log("Returning Firestore data");
                  return await responseSuccess(
                      {
                          carList: dataOut,
                      },
                      200
                  );
              }
          } else {
              console.log("Processing resCar data");
          }
  
          // SQL query for BCT_CONTRACT
          const sqlNew = `SELECT engineCode FROM BCT_CONTRACT WHERE REPLACE(centerPhone, '-', '') LIKE ? OR centerPhone LIKE ? GROUP BY engineCode`;
          const resNew = await connectdocker.connect(DatabaseConfig.BCT_AMS1, sqlNew, [
              formattedPhone,
              originalPhone,
          ]);
  
          const engineList = [
              ...new Set([
                  ...resCar.map(car => car.engin), // ดึงค่าจาก resCar
                  ...resNew.map(newEntry => newEntry.engineCode), // ดึงค่าจาก resNew
              ]),
          ];
  
          engineList.forEach((item, index) => {
              carList.push({
                  name: "",
                  phone: bodyData.phone,
                  reg: resCar[index]?.reg ?? "",
                  machine_number: item,
                  miles: [],
                  rustproof: [],
                  insurance: [],
                  pmg: [],
                  dataUpdate: new Date().toISOString()
              });
          });
  
          for (const car of carList) {
              const sqlContract = `SELECT BCT_CONTRACT.colorName, BCT_CONTRACT.colorCode, BCT_CONTRACT
  
  .carCode, MarginSale.car_model, MarginSale.car_model_sa, MarginSale.car_type
                      FROM BCT_CONTRACT LEFT JOIN MarginSale 
                      ON BCT_CONTRACT.carCode = MarginSale.carCode
                      WHERE BCT_CONTRACT.engineCode IN (?)`;
      
              const resContract = await connectdocker.connect(
                  DatabaseConfig.BCT_AMS1,
                  sqlContract,
                  [car.machine_number]
              );
      
              car.colorCode = resContract[0]?.colorCode ?? "";
              car.colorName = resContract[0]?.colorName ?? "";
              car.carCode = resContract[0]?.carCode ?? "";
              car.car_model = resContract[0]?.car_model ?? "";
              car.car_model_sa = resContract[0]?.car_model_sa ?? "";
              car.car_type = resContract[0]?.car_type ?? "";
  
              console.log(car.machine_number);
      
              const sql = `SELECT AFSInvoiceHistoryDetail.Column56, AFSInvoiceHistoryDetail.Column5, AFSInvoiceHistoryDetail.Column23, AFSInvoiceHistoryDetail.Column11,
                      AFSInvoiceHistoryReportSummary.Column35, AFSInvoiceHistoryReportSummary.Column28
                      FROM AFSInvoiceHistoryDetail LEFT JOIN AFSInvoiceHistoryReportSummary
                      ON AFSInvoiceHistoryDetail.Column56 = AFSInvoiceHistoryReportSummary.Column114
                      WHERE AFSInvoiceHistoryReportSummary.Column29 = ?
                      ORDER BY AFSInvoiceHistoryReportSummary.running DESC`;
      
              const res = await connectdocker.connect(DatabaseConfig.MIRAI, sql, [
                  car.machine_number,
              ]);
      
              var latestItem;
      
              // Set car_id and add mileage details for the selected latestItem
              if (res.length > 0) {
                  // Initialize latestItem as the first item by default
                  latestItem = res[0];
      
                  // Loop through `res` to find the first item where Column23 starts with "เช็กระยะ"
                  for (const item of res) {          
                      if (item.Column23.startsWith("เช็กตามระยะ")) {
                          console.log("checked");
                          latestItem = item;
                          break; // Stop the loop once we find the first matching item
                      }
                  }
  
                  car.car_id = latestItem.Column28 ?? "";
      
                  car.miles.push({
                      mileage_detail: latestItem.Column23 ?? "",
                      mileage: latestItem.Column35 ?? "",
                      mileage_date: latestItem.Column11 ?? "",
                  });
              } else {
                  car.car_id = "";
      
                  car.miles.push({
                      mileage_detail: "",
                      mileage: "",
                      mileage_date: "",
                  });
              }
      
              for (const item of res) {
                  let foundItem = null;
                  if (item.Column23.includes("สนิม")) {
                      car.rustproof.push({
                          rust_detail: item.Column23 ?? "",
                          rust_date: item.Column11 ?? "",
                      });
                      foundItem = item;
                      break;
                  }
              }
      
              const sqlInsurance = `SELECT running, insurance, isr_date_start_insurance, isr_type_date_end_insurance FROM BCT_INSURANCE WHERE engineCode = ? ORDER BY running DESC`;
      
              const resInsurance = await connectdocker.connect(
                  DatabaseConfig.BCT_AMS1,
                  sqlInsurance,
                  [car.machine_number]
              );
      
              if (resInsurance.length > 0) {
                  car.insurance.push({
                      insurance_detail: resInsurance[0]?.insurance ?? "",
                      insurance_date: resInsurance[0]?.isr_date_start_insurance ?? "",
                  });
              } else {
                  car.insurance.push({
                      insurance_detail: "",
                      insurance_date: "",
                  });
              }
  
              // if (car.carCode !== "" && car.colorCode !== "") {
              //     const resCarPic = await connectdocker.connect(
              //         DatabaseConfig.webPKG,
              //         `SELECT *
              //                 FROM CarForPKG
              //                 LEFT JOIN CarColorForPKG ON CarForPKG.car_id = CarColorForPKG.car_id
              //                 LEFT JOIN CarImageForPKG ON CarColorForPKG.color_id = CarImageForPKG.color_id
              //                 WHERE CarForPKG.model_name = ? AND CarColorForPKG.name = ?`,
              //         [car.car_model, car.colorCode]
              //     );
              //     car.car_image = resCarPic[0]?.image_url ?? "";
              //     console.log(resCarPic);
              // }
          }
  
          console.log('carList');
          console.log(carList);
          
          if (resFirebase == false) {
              for (let i = 0; i < carList.length; i++) {
                  await insertPhoneToFirestoreMenuMyCar(bodyData.phone, "myCarOwner/uxi3UzQy43KPi5QC8mN2", carList[i]);
              }
          } else {
              for (let i = 0; i < carList.length; i++) {
                  const docId = resFirebase.documents[i].name.split('/');
                  await updatePhoneInFirestoreMenuMyCar(bodyData.phone, "myCarOwner/uxi3UzQy43KPi5QC8mN2", docId[8], carList[i]);
              }
          }
  
          return await responseSuccess({ carList: carList }, 200);
      } catch (error) {
          console.error(`ERROR :: getCarFail => ${error}`);
          return await responseError(`ERROR :: getCarFail => ${error}`, 407);
      }
  }

    public async getCarPicture(req: any, env: any) {
      try {
          const bodyData = req.data;
          const resCarPic = await connectdocker.connect(
              DatabaseConfig.webPKG,
              `SELECT *
                              FROM CarForPKG
                              LEFT JOIN CarColorForPKG ON CarForPKG.car_id = CarColorForPKG.car_id
                              LEFT JOIN CarImageForPKG ON CarColorForPKG.color_id = CarImageForPKG.color_id
                              WHERE CarForPKG.model_name = ? AND CarColorForPKG.name = ?`,
              [bodyData.car_model, bodyData.colorCode]
            );
            const resOut = resCarPic[0]?.image_url ?? "";
            console.log(resCarPic);
          return await responseSuccess({image_url: resOut,},200);
      } catch (error) {
          console.error(`ERROR :: getImageFail => ${error}`);
          return await responseError(`ERROR :: getImageFail => ${error}`, 407);
      }
  }
}

export const CarOwnerModule = new CarOwnerController();