import { getPhoneDataFromFirestore, parseFirestoreData, insertPhoneToFirestoreMenuMyCar, updatePhoneInFirestoreMenuMyCar } from "../../../help/sendNotificationFCM";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class GetCouponRCController {
    // Single function to get PSI coupon
    public async getCoupon(req: any, env: any) {
        try {
            const bodyData = req.data;
            
            const result = await connectdocker.connect(
              DatabaseConfig.webPKG, 
              `SELECT running, car_owner_name, register_license, phone_owner, phone_get, status, point, expire_date 
              FROM couponRC 
              WHERE (phone_get = ? OR (phone_get IS NULL AND phone_owner = ?))
              AND running = (
                  SELECT MIN(running) 
                  FROM couponRC AS sub 
                  WHERE (sub.phone_get = ? OR (sub.phone_get IS NULL AND sub.phone_owner = ?))
                  AND DATE(sub.create_time) = DATE(couponRC.create_time)
              )
              ORDER BY running DESC;`,
              [bodyData.phone, bodyData.phone, bodyData.phone, bodyData.phone]
          );

          return await responseSuccess(result, 200);
        } catch (error) {
            console.error(`ERROR :: getCarFail => ${error}`);
            return await responseError(`ERROR :: getCarFail => ${error}`, 407);
        }
    }
}

export const GetCouponRCModule = new GetCouponRCController();