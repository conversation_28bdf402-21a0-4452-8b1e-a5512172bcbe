import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseSuccess } from "help/response";

class InsertCouponController {
    public async insertCoupon(req: any) {
        try {
            const params = await req.data;
    
            // สร้างวันที่ปัจจุบันในรูปแบบ YYYY-MM-DD
            const today = new Date().toISOString().slice(0, 10);
    
            const result = await connectdocker.connect(
                DatabaseConfig.webPKG,
                "INSERT INTO couponRC (phone_owner, register_license, car_owner_name, point, status, expire_date) VALUES (?, ?, ?, ?, ?, ?)",
                [params.phone_owner, params.register_license, params.car_owner_name, params.point, "0", today]
            );
    
            return await responseSuccess(result, 200);
        } catch(err) {
            console.log(err);
            return await responseSuccess(err, 500);
        }
    }    

    public async updateUseCoupon(req: any) {
        try {
            const params = await req.data;
            
            const result = await connectdocker.connect(
                DatabaseConfig.webPKG,
                "UPDATE couponRC SET status = ?, store = ? WHERE running = ?",
                ["1", params.store, params.running]
            );

            const dataShow = await connectdocker.connect(
                DatabaseConfig.webPKG,
                "SELECT car_owner_name, phone_owner, phone_get FROM couponRC WHERE running = ?",
                [params.running]
            );

            console.log(dataShow);
            

            var shop = "aroi";
            var phone = "";

            if(params.store == "2") {
                shop = "cu";
            }
            if(dataShow[0].phone_get != null && dataShow[0].phone_get != undefined) {
                phone = dataShow[0].phone_get;
            }else{
                phone = dataShow[0].phone_owner;
            }
            
            const dataPhoneName = await connectdocker.connect(
                DatabaseConfig.webPKG,
                "SELECT fullname FROM customercenter WHERE mobile = ?",
                [phone]
            );

            const newRequest = new Request(`${PAY_HOOK_URL}`, { 
                body: JSON.stringify({
                    "phone" : phone,
                    "name" : dataPhoneName[0].fullname,
                    "shop" : shop
                  }),
                headers: {
                "Content-Type": "application/json",
                "x-api-key": PAY_HOOK_KEY
                },
                method: "POST",
              });
                           

            const res = await fetch(newRequest);            

            return await responseSuccess(result, 200);
        } catch(err) {
            console.log(err);
            return await responseSuccess(err, 500);
        }
    }

    public async updateClaimCoupon(req: any) {
        try {
            const params = await req.data;
    
            // Step 1: ตรวจสอบว่ามี phone_get ในวันนี้หรือไม่
            const checkResult = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT running, car_owner_name, register_license, phone_owner, status, point, expire_date
                 FROM couponRC
                 WHERE phone_owner = ? 
                 AND DATE(create_time) = CURDATE()
                 AND phone_get IS NULL
                 ORDER BY running DESC
                 LIMIT 1`,
                [params.phone_owner]
            );
    
            // ถ้ามีข้อมูลที่สามารถใช้ได้
            if (checkResult.length > 0) {
                // Step 2: ตรวจสอบว่าเบอร์นี้ได้ใช้สิทธิ์วันนี้หรือยัง
                const existsResult = await connectdocker.connect(
                    DatabaseConfig.webPKG,
                    `SELECT 1
                     FROM couponRC
                     WHERE phone_get = ?
                     AND DATE(create_time) = CURDATE()
                     LIMIT 1`,
                    [params.phone_get]
                );
    
                // ถ้ายังไม่มี phone_get สำหรับวันนี้
                if (existsResult.length === 0) {
                    const coupon = checkResult[0];
    
                    // Step 3: ทำการ INSERT ข้อมูลใหม่
                    const insertResult = await connectdocker.connect(
                        DatabaseConfig.webPKG,
                        `INSERT INTO couponRC (car_owner_name, register_license, phone_owner, phone_get, status, point, expire_date, create_time)
                         VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
                        [
                            coupon.car_owner_name,
                            coupon.register_license,
                            coupon.phone_owner, 
                            params.phone_get,
                            "0",
                            coupon.point,
                            coupon.expire_date
                        ]
                    );
    
                    return await responseSuccess(insertResult, 200);
                } else {
                    return await responseSuccess({ message: "เบอร์นี้ได้ใช้สิทธิ์แล้วในวันนี้" }, 400);
                }
            } else {
                return await responseSuccess({ message: "ไม่มีคูปองสำหรับ Phone Owner นี้" }, 404);
            }
    
        } catch (err) {
            console.log(err);
            return await responseSuccess(err, 500);
        }
    }
    
}

export const InsertCouponModule = new InsertCouponController();