import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateCouponRC {

    public async ValidateCouponRC(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                phone: Joi.string()
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async ValidateCouponRCInsert(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                phone_owner: Joi.string(),
                register_license: Joi.string(),
                point: Joi.string(),
                car_owner_name: Joi.string(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async ValidateCouponRCClaim(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                phone_get: Joi.string(),
                phone_owner: Joi.string(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
    public async ValidateCouponRCUsed(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                running: Joi.number(),
                store: Joi.string(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

}

export const ValidationCouponRC = new ValidateCouponRC();