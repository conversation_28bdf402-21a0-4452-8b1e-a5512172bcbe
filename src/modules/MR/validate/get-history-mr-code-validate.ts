import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateGetHistoryMRCode {

    public async ValidateGetHistoryMRCode(req: any) {
        try {
            // console.log("Request Body:", req.body);  // เพิ่มการ print ค่าของ body เพื่อการ debug

            const params = await req.data;

            let schema = Joi.object({
                mr_code: Joi.string(),    
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationGetHistoryMRCode = new ValidateGetHistoryMRCode();
