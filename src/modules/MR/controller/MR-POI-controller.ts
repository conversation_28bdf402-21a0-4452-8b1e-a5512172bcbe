import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class MRPOIController {
    public async getMRPOI(req: any) {
        try {
            let listPOI = [593, 596, 599, 620, 629, 623, 626, 569, 570, 571, 567, 568, 574, 575, 602, 605];
    
            console.log(listPOI);
            
            const res = await connectdocker.connect(
                DatabaseConfig.webLDX,
                `SELECT running, typeApplication, type_bu, point, titile
                    FROM register_activity
                    WHERE running IN (?) 
                    ORDER BY CASE running
                        WHEN 593 THEN 1
                        WHEN 596 THEN 2
                        WHEN 599 THEN 3
                        WHEN 620 THEN 4
                        WHEN 629 THEN 5
                        WHEN 623 THEN 6
                        WHEN 626 THEN 7
                        WHEN 569 THEN 8
                        WHEN 570 THEN 9
                        WHEN 571 THEN 10
                        WHEN 567 THEN 11
                        WHEN 568 THEN 12
                        WHEN 574 THEN 13
                        WHEN 575 THEN 14
                        WHEN 602 THEN 15
                        WHEN 605 THEN 16
                    END`,
                [listPOI]
            );
            console.log(res);
    
            return responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return responseError("ERROR :: Get getMRPOI", 407);
        }
    }
    
}

export let MRPOIModule = new MRPOIController();