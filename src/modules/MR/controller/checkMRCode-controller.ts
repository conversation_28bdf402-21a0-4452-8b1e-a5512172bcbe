import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

// class CheckMRCodeController {
//     public async checkMRCode(req: any, env: any) {
//         try {
//             const bodyData = req.data;

//             // ตรวจสอบและกำหนดค่าให้ phone_number เป็น string เสมอ
//             const phoneNumber = bodyData.phone_number && bodyData.phone_number.trim() || ""; // ตรวจสอบและตัดช่องว่าง

//             // ค้นหา user_id และ mr_code ของ MR Code ที่ผู้ใช้กรอกมา
//             const mrResult = await connectdocker.connect(
//                 DatabaseConfig.webPKG, 
//                 `SELECT user_id, mr_code, full_name, phone_number FROM mr_pms_center WHERE mr_code = ?`,
//                 [bodyData.mr_code]
//             );

//             if (mrResult.length === 0) {
//                 return await responseError("ไม่พบ MR Code", 404);
//             }

//             const mrOwnerId = mrResult[0].user_id; // เจ้าของ MR Code
//             const mrOwnerPhoneNumber = mrResult[0].phone_number; // เบอร์โทรของเจ้าของ MR Code

//             // กรณี Add MR Code เท่านั้น (Register ไม่ต้องเช็คเบอร์)
//             if (phoneNumber) {
//                 // ตรวจสอบว่าเบอร์โทรที่ส่งมาตรงกับ MR Code หรือไม่
//                 if (phoneNumber === mrOwnerPhoneNumber) {
//                     return await responseError("เบอร์โทรศัพท์ไม่ตรงกับ MR Code นี้", 400);
//                 }

//                 // ค้นหา user_id ของผู้ใช้จากเบอร์โทร
//                 const userResult = await connectdocker.connect(
//                     DatabaseConfig.webPKG, 
//                     `SELECT id, fullname FROM customercenter WHERE mobile = ?`, 
//                     [phoneNumber]
//                 );

//                 if (userResult.length === 0) {
//                     return await responseError("ไม่พบบัญชีที่เกี่ยวข้องกับเบอร์โทรศัพท์นี้", 401);
//                 }

//                 const loggedInUserId = userResult[0].id; // user_id ของผู้ใช้ที่ล็อกอิน

//                 // เช็คว่า MR Code เป็นของตัวเองหรือไม่
//                 if (mrOwnerId === loggedInUserId) {
//                     return await responseError("คุณไม่สามารถใช้ MR Code ของตัวเองได้", 400);
//                 }

//                 // ทำการ update ข้อมูลใน customercenter ถ้าผ่านการเช็คแล้ว
//                 const updateResult = await connectdocker.connect(
//                     DatabaseConfig.webPKG,
//                     `UPDATE customercenter SET ref_code_mr = ? WHERE id = ?`,
//                     [bodyData.mr_code, loggedInUserId]
//                 );

//                 if (updateResult.affectedRows === 0) {
//                     return await responseError("ไม่สามารถอัพเดทข้อมูลได้", 500);
//                 }
//             }

//             return await responseSuccess(mrResult, 200);

//         } catch (error) {
//             console.error(`ERROR :: CheckFail => ${error}`);
//             return await responseError(`ERROR :: CheckFail => ${error}`, 500);
//         }
//     }
// }



// export const CheckMRCodeModule = new CheckMRCodeController();



class CheckMRCodeController {
    public async checkMRCode(req: any, env: any) {
      try {
        const bodyData = req.data;
        const phoneNumber = bodyData.phone_number?.trim() || "";
  
        // console.log("Received MR Code:", bodyData.mr_code);
        // console.log("MR Code chars:", bodyData.mr_code.split(""));
        // console.log("Phone number:", phoneNumber);
  
        // 🔹 ค้นหา MR Code ที่ส่งมา
        const mrResult = await connectdocker.connect(
          DatabaseConfig.webPKG,
          `SELECT user_id, mr_code, full_name, phone_number FROM mr_pms_center WHERE mr_code = ?`,
          [bodyData.mr_code]
        );
  
        if (mrResult.length === 0) {
          return await responseError("ไม่พบ MR Code", 404);
        }
  
        const mrOwnerId = mrResult[0].user_id;
        const mrOwnerPhoneNumber = mrResult[0].phone_number;
  
        // ถ้าไม่มีเบอร์โทร ไม่ต้องเช็คต่อ
        if (!phoneNumber) {
          return await responseSuccess(mrResult, 200);
        }
  
        // 🔹 ค้นหาผู้ใช้ปัจจุบัน
        const userResult = await connectdocker.connect(
          DatabaseConfig.webPKG,
          `SELECT id, COALESCE(NULLIF(ref_code_mr, ''), '') AS ref_code_mr FROM customercenter WHERE mobile = ?`,
          [phoneNumber]
        );
  
        if (userResult.length === 0) {
          return await responseError("ไม่พบบัญชีที่เกี่ยวข้องกับเบอร์โทรศัพท์นี้", 401);
        }
  
        const loggedInUserId = userResult[0].id;
        const referredByMrCode = userResult[0].ref_code_mr;
  
        // 🔹 ค้นหา ref_code_mr ของเจ้าของ MR Code
        const ownerResult = await connectdocker.connect(
          DatabaseConfig.webPKG,
          `SELECT id, ref_code_mr FROM customercenter WHERE mobile = ?`,
          [mrOwnerPhoneNumber]
        );
  
        if (ownerResult.length > 0) {
          const rawRefCodeMr = ownerResult[0].ref_code_mr;
          if (rawRefCodeMr) {
            const ownerRefCodeMr = rawRefCodeMr.replace("MR-0", "");
            if (ownerRefCodeMr === loggedInUserId.toString()) {
              return await responseError("คุณไม่สามารถใช้ MR Code ของผู้ที่แนะนำคุณได้", 201);
            }
          }
        }
  
        // ❌ ป้องกันการใช้ MR Code ของตัวเอง
        if (mrOwnerId === loggedInUserId) {
          return await responseError("คุณไม่สามารถใช้ MR Code ของตัวเองได้", 400);
        }
  
        // ❌ ป้องกันการเปลี่ยนแปลงรหัสผู้แนะนำ (ถ้ามีอยู่แล้ว)
        if (referredByMrCode) {
          return await responseError("คุณไม่สามารถเปลี่ยนรหัสผู้แนะนำได้", 403);
        }
  
        // ✅ อัปเดต ref_code_mr
        const updateResult = await connectdocker.connect(
          DatabaseConfig.webPKG,
          `UPDATE customercenter SET ref_code_mr = ? WHERE id = ?`,
          [bodyData.mr_code, loggedInUserId]
        );
  
        if (updateResult.affectedRows === 0) {
          return await responseError("ไม่สามารถอัพเดทข้อมูลได้", 500);
        }
  
        return await responseSuccess(mrResult, 200);
  
      } catch (error) {
        console.error(`ERROR :: CheckFail => ${error}`);
        return await responseError(`ERROR :: CheckFail => ${error}`, 500);
      }
    }
  }
  
  

export const CheckMRCodeModule = new CheckMRCodeController();
