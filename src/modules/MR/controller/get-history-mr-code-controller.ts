import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";


class GetHistoryMRCode{
    public async GetHistoryMRCode(req:any, env: any){

        try{
            const bodyData = req.data;

            // ✅ ตรวจสอบว่า mr_code ถูกส่งมาใน request หรือไม่
            if (!bodyData.mr_code) {
                return await responseError("กรุณาระบุ MR Code", 400);
            }

            // ✅ ค้นหาจำนวนครั้งที่ MR Code นี้ถูกใช้งานใน customercenter
            const countResult = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT COUNT(*) AS usage_count FROM customercenter WHERE ref_code_mr = ?`,
                [bodyData.mr_code] // ใช้ mr_code ที่ส่งมา
            );

            if (countResult.length === 0) {
                return await responseError("ไม่พบข้อมูล MR Code นี้", 404);
            }

            const usageCount = countResult[0].usage_count; // จำนวนครั้งที่ MR Code นี้ถูกใช้งาน

            // ✅ ส่งจำนวนการใช้งานกลับไปให้ผู้ใช้
            return await responseSuccess({ mr_code: bodyData.mr_code, usage_count: usageCount }, 200);

        }catch(error){
            console.error(`ERROR :: CountMRCodeUsageFail => ${error}`);
            return await responseError(`ERROR :: CountMRCodeUsageFail => ${error}`, 500);
        }
    }
}
export const  GetHistoryMRCodeModule = new GetHistoryMRCode();