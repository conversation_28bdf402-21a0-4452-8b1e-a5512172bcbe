import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class SaveActivity {
    public async saveActivity(req: any) {
        try {
            let bodyData = await req.data;

            let value = {
                user_id : bodyData.id.toString().trim(),
                menu: bodyData.menu.toString().trim(),
                note1: bodyData.note1.toString().trim(),
                note2: bodyData.note2,
                type: bodyData.type.toString().trim(),
            }
            
            let resSaveActivity = await connectdocker.connect(
                DatabaseConfig.webPKG,
                // `INSERT INTO activity SET ?`,
                "INSERT INTO activity (user_id, menu, note1, note2, type) VALUES (?, ?, ?, ?, ?)",
                [value.user_id, value.menu, value.note1, value.note2, value.type]
            )

            return await responseSuccess(resSaveActivity, 200);

        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let SaveActivityModule = new SaveActivity();