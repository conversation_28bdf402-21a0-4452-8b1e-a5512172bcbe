import Joi from "joi";
import {responseError} from "../../../help/response";

export class ValidateSaveActivity {
    
    public async ValidateSaveActivity(req: any){
        try {
            const params = await req.data;
            let schema = Joi.object({
                id: Joi.required(),
                menu: Joi.any(),
                note1: Joi.any(),
                note2: Joi.any(),
                type: Joi.required(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            console.log("=-=-=-=-=-=-=");
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

}

export let ValidationSaveActivity = new ValidateSaveActivity();