import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class UpdateRefCodeMrController {
    public async updateRefCodeMr(req: any, env: any) {
        try {
            const bodyData = req.data;
            const result = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT id,firstname,lastname,phone_firebase,mobile,advisor_id FROM customercenter WHERE mobile = ?`,
                [bodyData.phone]
            );

            if (!result || result.length === 0) {
                return responseError("ไม่พบข้อมูลใน customercenter", 404);
            }

            const customer = result[0];
            const advisorId = customer.advisor_id;

            // ✅ ตรวจสอบ advisor_id ต้องไม่เป็น null หรือว่าง
            if (!advisorId || advisorId.trim() === "") {
                return responseError("Advisor ID is missing or empty", 400);
            }

            // 🔹 ดึงข้อมูลพนักงาน
            const dateUser = await connectdocker.connect(
                DatabaseConfig.PPP7,
                `SELECT id, name_th, surname_th, telNumber FROM PKGemployee WHERE id = ?`,
                [advisorId]
            );

            if (!dateUser || dateUser.length === 0) {
                return responseError("ไม่พบข้อมูลพนักงาน", 404);
            }

            const user = dateUser[0];
            let telNumber = user?.telNumber?.trim() || "";

            if (!telNumber) {
                return responseError("ข้อมูลเบอร์โทรไม่ถูกต้อง", 400);
            }

            // ✅ จัดรูปแบบเบอร์โทรศัพท์
            telNumber = telNumber.trim(); // ลบช่องว่างหน้า-หลัง

            // ✅ ลบทุกอักขระที่ไม่ใช่ตัวเลข
            telNumber = telNumber.replace(/[^\d]/g, ""); // ลบทุกอย่างที่ไม่ใช่ตัวเลข

            // ✅ ถ้าหมายเลขขึ้นต้นด้วย "66" → ตัดออก
            if (telNumber.startsWith("66")) {
                telNumber = telNumber.substring(2);
            }

            // ✅ เพิ่ม "0" นำหน้าหมายเลข ถ้ายังไม่มี
            if (!telNumber.startsWith("0")) {
                telNumber = "0" + telNumber;
            }

            // 🔹 ค้นหา mr_code จาก mr_pms_center
            const mrData = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT user_id, mr_code, full_name FROM mr_pms_center WHERE phone_number LIKE ?`,
                [`%${telNumber}%`]
            );

            if (!mrData || mrData.length === 0) {
                return responseError("ไม่พบข้อมูลใน MR PMS Center", 404);
            }

            const mrInfo = mrData[0];

            // 🔹 อัปเดต `ref_code_mr` ใน `customercenter`
            const updateResult = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `UPDATE customercenter SET ref_code_mr = ? WHERE mobile = ?`,
                [mrInfo.mr_code, customer.mobile]
            );

            if (updateResult.affectedRows > 0) {
                return responseSuccess({
                    id: user.id,
                    name: user.name_th,
                    surname: user.surname_th,
                    telNumber: telNumber,
                    mrData: mrInfo,
                    message: "อัปเดต ref_code_mr สำเร็จ"
                }, 200);
            } else {
                return responseError("อัปเดตไม่สำเร็จ", 500);
            }
        } catch (e) {
            console.error(`ERROR: ${e}`);
            return responseError("เกิดข้อผิดพลาดในการเชื่อมต่อ", 500);
        }
    }
}

export const UpdateRefCodeMrModule = new UpdateRefCodeMrController();
