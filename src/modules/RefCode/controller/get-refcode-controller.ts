import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
class GetRefCodeController {
    public async getRefCode(req: any, env: any) {
        try {
            const bodyData = req.data;

            // ดึงข้อมูล id จาก API แรก
            const result = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT id, fullname, firstname, lastname, phone_firebase, mobile FROM customercenter WHERE id = ?`,
                [bodyData.id]
            );

            if (result.length === 0) {
                return responseError("ไม่พบข้อมูล", 404);
            }

            const userId = result[0].id; // ดึง id
            let phoneFirebase = result[0].phone_firebase; // ดึง phone_firebase

            // ✅ ลบ "-" ออกจาก phone_firebase ถ้ามี
            phoneFirebase = phoneFirebase.replace(/-/g, "");

            // ใช้ id ที่ได้ไปค้นหาใน mr_pms_center
            const users = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT user_id, mr_code, referral_code FROM mr_pms_center WHERE user_id = ?`,
                [userId]
            );

            if (users.length === 0) {
                return responseError("ไม่พบข้อมูลใน mr_pms_center", 404);
            }

            const { user_id, mr_code, referral_code } = users[0];

            if (!referral_code) { // ถ้า referral_code เป็น NULL หรือไม่มีค่า
                console.log(`User ID ${user_id} ไม่มี referral_code → เรียก API อื่น`);
                const apiResponse = await GetRefCodeController.callAnotherAPI(phoneFirebase);  // ✅ ใช้ GetRefCodeController แทน
                console.log("API Response:", apiResponse);

                // ถ้าได้ referralCode จาก API อื่น
                if (apiResponse && apiResponse.data && apiResponse.data.referralCode) {
                    const referralCode = apiResponse.data.referralCode;

                    // อัปเดต referral_code ในฐานข้อมูล mr_pms_center
                    await connectdocker.connect(
                        DatabaseConfig.webPKG,
                        `UPDATE mr_pms_center SET referral_code = ? WHERE user_id = ?`,
                        [referralCode, user_id]
                    );
                    console.log(`อัปเดต referral_code เป็น ${referralCode} สำหรับ user_id: ${user_id}`);
                    
                    // ✅ ส่งข้อความใน response
                    return responseSuccess({ message: `อัปเดต referral_code เป็น ${referralCode} สำหรับ user_id: ${user_id}` }, 200);
                }
            } else {
                console.log(`User ID ${user_id} มี referral_code แล้ว → ไม่ต้องเรียก API อื่น`);
                return responseSuccess(users, 200);
            }

            return responseSuccess(users, 200);  // ในกรณีที่ไม่ต้องอัปเดต
        } catch (e) {
            console.log(`ERROR ${e}`);
            return responseError("เกิดข้อผิดพลาด", 500);
        }
    }

    // ✅ ทำให้ callAnotherAPI เป็น static method
    public static async callAnotherAPI(phoneFirebase: string) {
        try {
            console.log(`เรียก API อื่นสำหรับ phone_firebase: ${phoneFirebase}`);

            const response = await fetch(`${API_URL_LIKEPOINT}/member/get-ref-code/${phoneFirebase}`, {
                headers: {
                    "Content-Type": "application/json",
                    "x-api-key": API_KEY_LIKEPOINT
                },
                method: "GET"
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error("เกิดข้อผิดพลาดในการเรียก API อื่น:", error);
            return null;
        }
    }
}

// ✅ เปลี่ยนเป็น static method และเรียกใช้แบบคลาส
export const GetRefCodeModule = new GetRefCodeController();
