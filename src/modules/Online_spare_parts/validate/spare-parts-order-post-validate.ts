import Joi from "joi";
import { responseError } from "../../../help/response";

export class ValidateOrderSparePartsPost {
  public async ValidateOrderSparePartsPost_(req: any) {
    try {
      const params = await req.data;

      // สร้าง schema สำหรับตรวจสอบฟิลด์ต่างๆ
      let schema = Joi.object({
        // status_order: Joi.string().required(),
        // tracking_shipping: Joi.string().required(),
        // slip_payment: Joi.string()
          // .regex(/^(https?:\/\/)?[\w.-]+(\.[\w\.-]+)+[/#?]?.*$/)
          // .required(),
        // discount_points: Joi.number().integer().min(0).required(),
        // admin_remarks: Joi.string().allow(null, ''),
        // shipping_address_id: Joi.number().integer().required(),
        // shipping_type: Joi.string().required(),
        // shipping_cost: Joi.number().precision(2).required(),
        // total_amount: Joi.number().precision(2).required(),
        // provider_id: Joi.number().integer().required(),
        // order_date: Joi.date().iso().required(),
        address_id: Joi.string().required(),
        member_id: Joi.number().integer().required(),
        shipping_cost: Joi.number().integer().required(),
        // address_line: Joi.string().required(),
        // sub_district: Joi.string().required(),
        // district: Joi.string().required(),
        // province: Joi.string().required(),
        // postal_code: Joi.string().required(),
        // country: Joi.string().required(),
        items: Joi.array()
          .items(
            Joi.object({
              product_id: Joi.string().required(),
              product_code: Joi.string().required(),
              quantity: Joi.number().integer().min(1).required(),
              price: Joi.number().precision(2).min(0).required(),
            })
          )
          .min(1)
          .required(),
      });

      // ตรวจสอบ validation
      const { error } = await schema.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }
}

export let ValidationOrderSparePartsPost = new ValidateOrderSparePartsPost();