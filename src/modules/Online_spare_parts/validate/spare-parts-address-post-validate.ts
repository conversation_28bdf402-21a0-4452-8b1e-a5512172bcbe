import Joi from "joi";
import { responseError } from "../../../help/response";

export class ValidateAddressSparePartsPost {
  public async validateAddressInsert(req: any) {
    try {
      const params = await req.data;

      // สร้าง schema สำหรับตรวจสอบฟิลด์ต่างๆ
      let schema = Joi.object({
        member_id: Joi.number().integer().required(),
        fullname: Joi.string(),
        address_line: Joi.string(),
        sub_district: Joi.string(),
        district: Joi.string(),
        province: Joi.string(),
        postal_code: Joi.string(),
        country: Joi.string(),
      });

      // ตรวจสอบ validation
      const { error } = await schema.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }

  public async validateAddressUpdate(req: any) {
    try {
      const params = await req.data;

      // สร้าง schema สำหรับตรวจสอบฟิลด์ต่างๆ
      let schema = Joi.object({
        member_id: Joi.number().integer().required(),
        fullname: Joi.string(),
        address_line: Joi.string(),
        sub_district: Joi.string(),
        district: Joi.string(),
        province: Joi.string(),
        postal_code: Joi.string(),
        country: Joi.string(),
        address_id: Joi.number().integer().required(),
      });

      // ตรวจสอบ validation
      const { error } = await schema.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }

  public async validateAddressDelete(req: any) {
    try {
      const params = await req.data;

      // สร้าง schema สำหรับตรวจสอบฟิลด์ต่างๆ
      let schema = Joi.object({
        member_id: Joi.number().integer().required(),
        address_id: Joi.number().integer().required(),
      });

      // ตรวจสอบ validation
      const { error } = await schema.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }

  public async validateAddressSetDefault(req: any) {
    try {
      const params = await req.data;
  
      // สร้าง schema สำหรับตรวจสอบฟิลด์ต่างๆ
      let schema = Joi.object({
        member_id: Joi.number().integer().required(),
        address_id: Joi.number().integer().required(),
      });
  
      // ตรวจสอบ validation
      const { error } = await schema.validate(params);
  
      // ถ้ามี error ใน validation ให้ส่ง responseError กลับไป
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }
}

export let ValidationAddressSparePartsPost = new ValidateAddressSparePartsPost();