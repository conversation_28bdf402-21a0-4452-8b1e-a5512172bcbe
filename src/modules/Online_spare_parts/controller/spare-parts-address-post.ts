import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class ShippingAddressesAPI {
  public async insertShippingAddress(req: any) {
    try {
      let bodyData = await req.data;
  
      const insertQuery = `
        INSERT INTO shipping_addresses (
          member_id, fullname, address_line, sub_district, district, province, postal_code, country
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
  
      // ทำการ insert ข้อมูลเข้าไปในตาราง shipping_addresses
      const result = await connectdocker.connect(DatabaseConfig.webPKG, insertQuery, [
        bodyData.member_id,
        bodyData.fullname,
        bodyData.address_line,
        bodyData.sub_district,
        bodyData.district,
        bodyData.province,
        bodyData.postal_code,
        bodyData.country,
      ]);
  
      // ตรวจสอบว่ามี lastInsertId หรือไม่
      if (result && result.lastInsertId) {
        const addressId = result.lastInsertId;
        return await responseSuccess({ message: "Insert successful", address_id: addressId }, 200);
      } else {
        return await responseError("Insert failed: no insertId returned", 500);
      }
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }

  public async updateShippingAddress(req: any) {
    try {
      let bodyData = await req.data;

      const updateQuery = `
        UPDATE shipping_addresses
        SET address_line = ?, sub_district = ?, district = ?, province = ?, postal_code = ?, country = ?,fullname = ?
        WHERE member_id = ? and address_id = ?
      `;

      const connection = await connectdocker.connect(DatabaseConfig.webPKG, updateQuery, [
        bodyData.address_line,
        bodyData.sub_district,
        bodyData.district,
        bodyData.province,
        bodyData.postal_code,
        bodyData.country,
        bodyData.fullname,
        bodyData.member_id,
        bodyData.address_id,
      ]);

      return await responseSuccess({ message: "Update successful" }, 200);
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }

  public async deleteShippingAddress(req: any) {
    try {
      let bodyData = await req.data;

      const deleteQuery = `
        DELETE FROM shipping_addresses
        WHERE member_id = ? and address_id = ?
      `;

      const connection = await connectdocker.connect(DatabaseConfig.webPKG, deleteQuery, [
        bodyData.member_id,
        bodyData.address_id,
      ]);

      return await responseSuccess({ message: "Delete successful" }, 200);
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }

  public async setDefaultShippingAddress(req: any) {
    try {
      let bodyData = await req.data;
  
      // ถ้า address_id เป็น -1 ให้ reset ทั้งหมดโดยไม่ตั้งค่า default ใดๆ
      if (bodyData.address_id === -1) {
        const resetAllQuery = `
          UPDATE shipping_addresses
          SET type_default = 0
          WHERE member_id = ?
        `;
  
        await connectdocker.connect(DatabaseConfig.webPKG, resetAllQuery, [bodyData.member_id]);
  
        return await responseSuccess({ message: "All addresses set to non-default" }, 200);
      }
  
      // Query สำหรับ reset type_default = False สำหรับที่อยู่ทั้งหมดของ member_id นี้ โดยไม่ไปยุ่งกับฟิลด์อื่นๆ
      const resetDefaultQuery = `
        UPDATE shipping_addresses
        SET type_default = 0
        WHERE member_id = ?
      `;
  
      // Query สำหรับตั้งค่า default ให้กับที่อยู่ที่เลือก (address_id) โดยไม่ไปยุ่งกับฟิลด์อื่นๆ
      const setDefaultQuery = `
        UPDATE shipping_addresses
        SET type_default = 1
        WHERE member_id = ? AND address_id = ?
      `;
  
      // ขั้นแรก reset type_default สำหรับทุก address_id ของ member_id นี้
      await connectdocker.connect(DatabaseConfig.webPKG, resetDefaultQuery, [bodyData.member_id]);
  
      // ขั้นสองตั้งค่า default ให้กับ address_id ที่ต้องการ โดยฟิลด์อื่นๆ จะไม่ถูกเปลี่ยนแปลง
      await connectdocker.connect(DatabaseConfig.webPKG, setDefaultQuery, [bodyData.member_id, bodyData.address_id]);
  
      return await responseSuccess({ message: "Default address set successfully" }, 200);
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }
}

export let ShippingAddressesModule = new ShippingAddressesAPI();