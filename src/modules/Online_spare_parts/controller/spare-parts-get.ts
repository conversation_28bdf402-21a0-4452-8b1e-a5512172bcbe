import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class SparePartsGetAll {
  public async getAllSpareParts(req: any) {
    try {
      let res = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT * 
        FROM spare_parts_pic 
        ORDER BY running DESC`,
        []
      );

      if (res.length === 0) {
        return await responseError("No spare parts found", 404);
      }

      // จัดกลุ่มตาม type และ category และเปลี่ยน item.upload เป็น array
      let groupedResult: any = {};

      res.forEach((item: any) => {
        const { type, category, upload, rates } = item;

        if (!groupedResult[type]) {
          groupedResult[type] = {};
        }

        if (!groupedResult[type][category]) {
          groupedResult[type][category] = [];
        }

        // แปลง upload ให้เป็น array
        const uploadsArray = upload ? upload.split(",") : [];

        // แปลง rates ให้เป็น JSON
        const ratesArray = rates ? JSON.parse(rates) : [];

        // เพิ่มเข้าไปในกลุ่ม
        groupedResult[type][category].push({
          ...item,
          upload: uploadsArray,
          rates: ratesArray, // แปลง rates เป็น JSON
        });
      });

      return await responseSuccess(groupedResult, 200);
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }
}

export let SparePartsGetModule = new SparePartsGetAll();