import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class ShippingAddressGet {
  public async getShippingAddresses(req: any) {
    try {
      let memberId = req.params.member_id;

      // Query สำหรับดึงข้อมูลจากตาราง shipping_addresses
      const addressQuery = `
        SELECT * FROM shipping_addresses 
        WHERE member_id = ?
      `;

      // ดึงข้อมูลจากตาราง shipping_addresses
      const addressResult = await connectdocker.connect(DatabaseConfig.webPKG, addressQuery, [memberId]);

      // ตรวจสอบว่าพบข้อมูลหรือไม่
      if (addressResult.length === 0) {
        return await responseError("Shipping address not found", 404);
      }

      // ส่งผลลัพธ์ที่ดึงได้ในรูปแบบ JSON
      return await responseSuccess(addressResult, 200);
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }
}

export let ShippingAddressGetModule = new ShippingAddressGet();