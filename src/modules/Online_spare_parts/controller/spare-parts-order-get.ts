import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class OrderSparePartsGet {
  public async getOrderDetails(req: any) {
    try {
      let memberId = req.params.member_id; // รับ member_id จาก request params

      // Query สำหรับดึงข้อมูล order_id และ items พร้อมกับข้อมูลจาก spare_parts_pic
      const orderQuery = `
        SELECT
            o.order_id,
            o.qr_data,
            o.qr_code_expire,
            o.tx_point,
            o.status_order,
            o.discount_points,
            o.shipping_address_id,
            o.order_placed_date,
            o.order_confirmed_date,
            o.awaiting_payment_date,
            o.payment_completed_date,
            o.preparing_order_date,
            o.ready_to_ship_date,
            o.shipped_date,
            o.awaiting_delivery_date,
            o.delivered_date,
            o.cancelled_date,
            o.slip_payment,
            o.tracking_shipping,
            COALESCE(i.item_id, i_del.item_id) AS item_id,
            COALESCE(i.product_id, i_del.product_id) AS product_id,
            COALESCE(i.product_code, i_del.product_code) AS product_code,
            COALESCE(i.quantity, i_del.quantity) AS quantity,
            COALESCE(i.price, i_del.price) AS price,
            s.upload AS spare_parts_upload,
            s.rates
        FROM orders_auto_parts o
        LEFT JOIN orders_auto_parts_items i 
            ON o.order_id = i.order_id 
            AND o.status_order != 'CANCELLED'
        LEFT JOIN orders_auto_parts_items_del i_del 
            ON o.order_id = i_del.order_id 
            AND o.status_order = 'CANCELLED'
        LEFT JOIN MIRAI.spare_parts_pic s 
            ON COALESCE(i.product_id, i_del.product_id) = s.parts_number
        WHERE o.member_id = ?
        ORDER BY o.order_date DESC
      `;

      // ดึงข้อมูลจาก database รอบเดียว
      const orderResult = await connectdocker.connect(DatabaseConfig.webPKG, orderQuery, [memberId]);

      // ตรวจสอบว่าเจอ order หรือไม่
      if (orderResult.length === 0) {
        return await responseError("Order not found", 404);
      }

      console.log(orderResult);
    

      // จัดกลุ่ม items ตาม order_id
      const orders = orderResult.reduce((acc: any, row: any) => {
        var {
          order_id,
          qr_data,
          qr_code_expire,
          tx_point,
          status_order,
          discount_points,
          shipping_address_id,
          order_placed_date,
          order_confirmed_date,
          awaiting_payment_date,
          payment_completed_date,
          preparing_order_date,
          ready_to_ship_date,
          shipped_date,
          awaiting_delivery_date,
          delivered_date,
          cancelled_date,
          slip_payment,
          tracking_shipping,
          item_id,
          product_id,
          product_code,
          quantity,
          price,
          rates,
          spare_parts_upload,
        } = row;

        if (!acc[order_id]) {
          acc[order_id] = {
            order_id,
            qr_data,
            qr_code_expire,
            tx_point,
            status_order,
            discount_points,
            shipping_address_id,
            order_placed_date,
            order_confirmed_date,
            awaiting_payment_date,
            payment_completed_date,
            preparing_order_date,
            ready_to_ship_date,
            shipped_date,
            awaiting_delivery_date,
            delivered_date,
            cancelled_date,
            slip_payment,
            tracking_shipping,
            items: [],
          };
        }

        // acc[order_id].items.push({
        //   item_id,
        //   product_id,
        //   product_code,
        //   quantity,
        //   price,
        //   rates,
        //   spare_parts_upload,
        // });

        // แปลง upload ให้เป็น array
        const uploadsArray = spare_parts_upload ? spare_parts_upload.split(",") : [];

        // แปลง rates ให้เป็น JSON

        const ratesArray = rates ? JSON.parse(rates) : [];

        // เพิ่ม item ลงใน order
        acc[order_id].items.push({
          running : item_id,
          product_id,
          product_code,
          quantity,
          price,
          rates: ratesArray, // แปลง rates เป็น JSON
          spare_parts_upload: uploadsArray[0] // เพิ่มฟิลด์ upload ที่ดึงมาจาก spare_parts_pic
        });

        return acc;
      }, {});

      const ordersArray = Object.values(orders);

      // Sort the ordersArray by order_id
      ordersArray.sort((a, b) => b.order_id - a.order_id);

      // จัดรูปแบบข้อมูล
      const response = {
        data: ordersArray
      };

      return await responseSuccess(response, 200);
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }
}

export let OrderSparePartsGetModule = new OrderSparePartsGet();