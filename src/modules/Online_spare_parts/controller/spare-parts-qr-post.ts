import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class SparePartsGet {
  insertOrderSpareParts(arg0: string, DecodeBody: (req: any) => Promise<Response | undefined>, ValidateSparePartsPost_: (req: any) => Promise<Response | undefined>, insertOrderSpareParts: any) {
    throw new Error("Method not implemented.");
  }
  public async getSpareParts(req: any) {
    try {
      let bodyData = await req.data;
      let res = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT * 
        FROM spare_parts_pic 
        WHERE parts_number LIKE CONCAT('%', ?, '%') 
        OR product_name LIKE CONCAT('%', ?, '%') 
        ORDER BY running DESC`,
        [bodyData.value, bodyData.value]
      );
      if (res.length === 0) {
        return await responseError("getSpareParts not found", 404);
      }
      
      // จัดกลุ่มตาม type และ category และเปลี่ยน item.upload เป็น array
      let groupedResult: any = {};

      res.forEach((item: any) => {
        const { type, category, upload } = item;

        if (!groupedResult[type]) {
          groupedResult[type] = {};
        }

        if (!groupedResult[type][category]) {
          groupedResult[type][category] = [];
        }

        // แปลง upload ให้เป็น array
        const uploadsArray = upload ? upload.split(",") : [];

        // เพิ่มเข้าไปในกลุ่ม
        groupedResult[type][category].push({
          ...item,
          upload: uploadsArray,
        });
      });

      return await responseSuccess(groupedResult, 200);
    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }
}

export let SparePartsPostModule = new SparePartsGet();