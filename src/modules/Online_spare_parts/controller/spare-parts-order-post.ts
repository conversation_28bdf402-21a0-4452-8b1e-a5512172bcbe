import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";
import { NotificationModule } from "../../Notification/controller/notification-controller";
import { addOrUpdateNotificationData } from "../../../help/sendNotificationFCM";
import { TGNotify } from "help/TG-notify";


class OrderSparePartsPost {
  public async insertOrderSpareParts(req: any) {
    try {
      var bodyData = await req.data;

      bodyData.address_id = parseInt(bodyData.address_id);
      const querygetAddress = `
        SELECT * FROM shipping_addresses WHERE address_id = ?
      `;

      let addressResult = [];

      // ถ้า address_id เป็น -1 ให้ใช้ข้อมูลกำหนดเอง
      if (bodyData.address_id === -1) {
        addressResult = [
          {
            address_line: "",
            sub_district: "",
            district: "",
            province: "",
            postal_code: "",
            country: "",
          },
        ];
      } else {
        addressResult = await connectdocker.connect(
          DatabaseConfig.webPKG,
          querygetAddress,
          [bodyData.address_id]
        );

        if (addressResult.length === 0) {
          return await responseError("Address not found", 404);
        }
      }

      // กำหนดรูปแบบวันที่ปัจจุบัน (แบบ POYYYYMM)
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, "0");
      const currentDateString = `${year}${month}`; // รูปแบบ YYYYMM

      // Query เพื่อหา tracking_shipping ล่าสุดที่ตรงกับเดือนปัจจุบัน
      const queryLastOrderId = `
        SELECT order_code 
        FROM orders_auto_parts 
        WHERE order_code LIKE 'PO${currentDateString}%' 
        ORDER BY order_code DESC 
        LIMIT 1
      `;

      const lastOrderResult = await connectdocker.connect(
        DatabaseConfig.webPKG,
        queryLastOrderId,
        []
      );

      let newOrder_code = "";

      if (lastOrderResult.length === 0) {
        // ถ้ายังไม่มี order_code ในเดือนนั้น ให้เริ่มต้นด้วย POYYYYMM0001
        newOrder_code = `PO${currentDateString}0001`;
      } else {
        console.log(lastOrderResult);
        // ถ้ามีแล้ว ให้นำ order_code ล่าสุดมา +1
        const lastOrderCode = lastOrderResult[0].order_code;

        // ตรวจสอบว่าเลขลำดับ 4 หลักสุดท้ายถูกต้องหรือไม่
        const lastOrderNumber = parseInt(lastOrderCode.slice(-4));
        if (isNaN(lastOrderNumber)) {
          throw new Error("Invalid order code format");
        }
        console.log(lastOrderNumber);

        // เพิ่มลำดับทีละ 1 และเติมเลข 0 ให้ครบ 4 หลัก
        const newOrderNumber = (lastOrderNumber + 1)
          .toString()
          .padStart(4, "0");
        newOrder_code = `PO${currentDateString}${newOrderNumber}`;
        console.log(newOrder_code);
      }

      // คำนวณ total_amount จาก quantity * price ของแต่ละ item
      let totalAmount = 0;
      for (const item of bodyData.items) {
        totalAmount += item.quantity * item.price;
      }
      //เช็คว่ามี shipping_cost หรือไม่
      if (bodyData.shipping_cost) {
        totalAmount += bodyData.shipping_cost;
      }

      // Insert ข้อมูลลงในตาราง orders_auto_parts
      const orderInsertQuery = `
        INSERT INTO orders_auto_parts (
          order_code, shipping_address_id, shipping_cost, member_id, address_line, sub_district, district, province, postal_code, country, total_amount, shipping_type ,order_placed_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `;
      const orderInsertParams = [
        newOrder_code,
        bodyData.address_id,
        bodyData.shipping_cost,
        bodyData.member_id,
        addressResult[0].address_line,
        addressResult[0].sub_district,
        addressResult[0].district,
        addressResult[0].province,
        addressResult[0].postal_code,
        addressResult[0].country,
        totalAmount,
        bodyData.address_id === -1 ? "PICK_UP" : "DELIVERY" ,
      ];

      const orderResult = await connectdocker.connect(
        DatabaseConfig.webPKG,
        orderInsertQuery,
        orderInsertParams
      );
      const orderId = orderResult.lastInsertId;

      // Insert ข้อมูล items ลงในตาราง orders_auto_parts_items
      const itemInsertQuery = `
        INSERT INTO orders_auto_parts_items (
          order_id, product_id, product_code, quantity, price
        ) VALUES (?, ?, ?, ?, ?)
      `;
      for (const item of bodyData.items) {
        const itemInsertParams = [
          orderId,
          item.product_id,
          item.product_code,
          item.quantity,
          item.price,
        ];
        await connectdocker.connect(
          DatabaseConfig.webPKG,
          itemInsertQuery,
          itemInsertParams
        );
      }

      // TODO :: GET MEMBER INFO
      let memberPhone = "";
      let memberName = "";
      const queryMemberInfo = `
        SELECT mobile, fullname
        FROM customercenter 
        WHERE id = ? 
        LIMIT 1
      `;
      const memberInfoResult = await connectdocker.connect(
        DatabaseConfig.webPKG,
        queryMemberInfo,
        [bodyData.member_id]
      );
      
      if (memberInfoResult.length != 0) {
        memberPhone = memberInfoResult[0].mobile;
        memberName = memberInfoResult[0].fullname;
      }

      let useName = "";

      if(bodyData.address_id == -1){        
        useName = memberName;
      }else{
        useName = addressResult[0].fullname;
      }
      // TODO :: GET MEMBER INFO

      let message = `แจ้งสั่งซื้อสินค้าอะไหล่ประดับยนต์กับบริษัทประชากิจฯ\nคุณ ${useName}\nหมายเลขคำสั่งซื้อ ${newOrder_code}\nรายการที่สั่งซื้อ\n`;
      let messageTG = `สั่งซื้ออะไหล่ประดับยนต์ผ่านAPP \n\nคุณ :: ${useName}\nเบอร์โทร :: ${memberPhone} \nหมายเลขคำสั่งซื้อ ${newOrder_code}\nรายการที่สั่ง ::\n`;      
      for (const item of bodyData.items) {
          message += `${item.product_code}  ${item.quantity} รายการ\n`;
          messageTG += `${item.product_code}  ${item.quantity} รายการ\n`;
      }

      message += ` ราคา ${Number(totalAmount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} บาท\n\nขอบคุณสำหรับการสั่งซื้อสินค้ากับทางบริษัทฯ`;
      messageTG += ` ราคา ${Number(totalAmount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} บาท\n\n@oraphanaks\n@tharatepsuk\n@mod_191022\n@chakhrithong`;


      let bodyNotification = {
          phone: memberPhone,
          title: "สั่งซื้อสินค้าเรียบร้อยแล้ว",
          detail: message.substring(0, 100),
      };

      NotificationModule.sendNotificationAppPMSINAPI(bodyNotification);
      const bodyNotiInApp = {
        fields: {
          title: { stringValue: "สั่งซื้อสินค้าเรียบร้อยแล้ว" }, // String
          detail: { stringValue: message }, // String
          type: { stringValue: "notificationLink" }, // String
          url: { stringValue: "" }, // String
          create: { timestampValue: { seconds: Math.floor(Date.now() / 1000), nanos: 0 } } // Firestore Timestamp
        }
      };

      // console.log("messageTG");
      // console.log(messageTG);
      addOrUpdateNotificationData(memberPhone, bodyNotiInApp);
      await TGNotify(messageTG, "-4561406239");

      // if (resNotification) {
      //     console.log("Notification ส่งสำเร็จ:", resNotification);
      // } else {
      //     console.error("ส่ง Notification ล้มเหลว");
      // }

      return await responseSuccess({ orderId: orderId}, 200);

    } catch (error) {
      console.log(error);
      return await responseError(error, 407);
    }
  }
}

export let OrderSparePartsPostModule = new OrderSparePartsPost();
