import { Request } from "itty-router";
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
// import { fetchData } from "../../../help/fetch";
import { FirebaseConfig } from "../../../help/firebase.db";
import { lineNotify } from "../../../help/line-notify";
import { fetchRequestService } from "help/fetchButTS";
import { sendNotificationFCM } from "../../../help/sendNotificationFCM";

class NotificationController {

  public async sendNotificationInAppPMS(req: any){
        try{
            console.log("sendNotification");
            const params = await req.data;

            var ResponseData = await fetchRequestService.post(
              `https://dw3xxotzc8.execute-api.ap-southeast-1.amazonaws.com/latest/sendNotificationInAppPMSByPhone`,
              {
                "phone": params.phone,
                "title": params.title ?? "",
                "type": params.type ?? "",
                "url": params.url ?? "", 
                "detail": params.detail ?? "",
                "note": params.note ?? "",
               },
              {
                "Content-Type": "application/json",
                "Authorization": FirebaseConfig.auth
              }
            );
            return responseSuccess(ResponseData, 200);
            

          }catch (e){
            console.log(e);
            return await responseError("ERROR :: sendNotification ", 407);
          }
  }

  public async sendNotificationAppPMS(req: any){
      try{
          console.log("sendNotification");
          const params = await req.data;
          let phone = params.phone;
          let resToken = await connectdocker.connect(
              DatabaseConfig.webPKG,
              `SELECT fullname, tokenMessage FROM customercenter WHERE mobile = ?`,
              [phone]
            )
            console.log(resToken);
            
            let token = resToken[0]["tokenMessage"];
            console.log(params.title);
            console.log(params.detail);
            const checkType = params.type ?? "1";
            const sendRunning = params.running ?? "0";

            if (token != "") {
              console.log(token);
              
              const ResponseData = await sendNotificationFCM(token, params.detail, params.title, checkType, sendRunning);
              // const ResponseData = await fetchRequestService.post(
              //     `https://fcm.googleapis.com/fcm/send`,
              //   {
              //       "to" : token,
              //       "notification" : {
              //           "body" : params.detail,
              //           "title" : params.title,
              //       },
              //       "data": {
              //         "type": checkType,
              //       }
              //   },
              //   {
              //       "Content-Type": "application/json",
              //       "Authorization": FirebaseConfig.auth
              //   }
              // );
              console.log(ResponseData);
              return responseSuccess(ResponseData, 200);
            }

        }catch (e){
          console.log(e);
          return await responseError("ERROR :: sendNotification ", 407);
        }
  }

  public async sendNotificationAppPMSINAPI(item: any){
    try{
      console.log("sendNotification");
      const {phone, title, detail} = item;
      let resToken = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT fullname, tokenMessage FROM customercenter WHERE mobile = ?`,
        [phone]
      )
      console.log(resToken);

      let token = resToken[0]["tokenMessage"];

      const checkType ="1";
      const sendRunning = "0";

      if (token != "") {
        console.log(token);

        const ResponseData = await sendNotificationFCM(token, detail, title, checkType, sendRunning);
        // const ResponseData = await fetchRequestService.post(
        //     `https://fcm.googleapis.com/fcm/send`,
        //   {
        //       "to" : token,
        //       "notification" : {
        //           "body" : params.detail,
        //           "title" : params.title,
        //       },
        //       "data": {
        //         "type": checkType,
        //       }
        //   },
        //   {
        //       "Content-Type": "application/json",
        //       "Authorization": FirebaseConfig.auth
        //   }
        // );
        console.log(ResponseData);
        return responseSuccess(ResponseData, 200);
      }

    }catch (e){
      console.log(e);
      return await responseError("ERROR :: sendNotification ", 407);
    }
  }


  public async sendNotificationAppPMSByPass(req: any) {
    try {
      const params = await req.data;
      var phone = params.phone;
      console.log(typeof phone);
      
      const checkPhone = phone.substring(0, 1);
      console.log(checkPhone);
      
      if(checkPhone != '0'){
        phone = "0" + params.phone.substring(3);
      }

      var res = await fetchRequestService.post(
        `https://dw3xxotzc8.execute-api.ap-southeast-1.amazonaws.com/latest/sendNotificationInAppPMSByPhone`,
        {
          "phone": phone,
          "title": params.title ?? "",
          "type": params.type ?? "",
          "url": params.url ?? "", 
          "detail": params.detail ?? "",
          "note": params.note ?? "",
         },
        {
          "Content-Type": "application/json",
          "Authorization": FirebaseConfig.auth
        }
      );
      console.log(res);
      if (res != false) {
        console.log("sendNotification");
          let resToken = await connectdocker.connect(
              DatabaseConfig.webPKG,
              `SELECT fullname, tokenMessage FROM customercenter WHERE mobile = ?`,
              [phone]
            )
            console.log(resToken);
            
            let token = resToken[0]["tokenMessage"];
            console.log(params.title);
            console.log(params.detail);
            const checkType = params.type ?? "0";
            const sendRunning = params.running ?? "0";

            if (token != "") {
              console.log(token);
              
              await sendNotificationFCM(token, params.detail, params.title, checkType, sendRunning);
            }
        return responseSuccess({ "message":"insert firebase success" }, 200);
      } else {
        return responseError("ERROR :: not inserted data ", 204);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }

//   public async sendNotificationAppPMSInAPI(bodyNotification: any) {
//     try {
//         let { phone, title, type, detail } = bodyNotification;  // ไม่ต้องใช้ await
//         console.log(phone, title, type, detail);
        
//         // ตรวจสอบและปรับเบอร์โทรศัพท์
//         const checkPhone = phone.substring(0, 1);
//         console.log(checkPhone);
//         if (checkPhone !== '0') {
//             phone = "0" + phone.substring(3);
//         }

//         // กำหนด URI และข้อมูลที่จะส่ง
//         const uri = 'https://dw3xxotzc8.execute-api.ap-southeast-1.amazonaws.com/latest/sendNotificationInAppPMSByPhone';
//         const bodyData = 
//         {
//             phone: phone,
//             title: title || "",
//             type: type || "",
//             detail: detail || "",
//         };
//         const headers = {
//             "Content-Type": "application/json",
//         };

//         // สร้างคำขอใหม่
//         const newRequest = new Request(uri, {
//             body: JSON.stringify(bodyData),
//             headers: headers,
//             method: "POST",
//         });

//         // ส่งคำขอและรอผลลัพธ์
//         const response = await fetch(newRequest);
//         const result = await response.json(); // แปลงผลลัพธ์เป็น JSON
//         console.log("Response:", result); // แสดงผลลัพธ์

//         // ตรวจสอบสถานะการตอบกลับ
//         if (result) {
//             console.log("sendNotification");

//             let resToken = await connectdocker.connect(
//                 DatabaseConfig.webPKG,
//                 `SELECT fullname, tokenMessage FROM customercenter WHERE mobile = ?`,
//                 [phone]
//             );
//             console.log(resToken);
            
//             let token = resToken[0]?.["tokenMessage"]; // ใช้ optional chaining
//             const checkType = type || "0"; // ถ้าไม่มีค่าให้เป็น "0"

//             if (token) { // เช็คว่า token ไม่ว่าง
//                 console.log(token);
//                 await fetchData(token, detail, title, checkType, 0, FirebaseConfig.auth);
//             }

//             return responseSuccess({ "message": "insert firebase success" }, 200);
//         } else {
//             return responseError("ERROR :: not inserted data ", 204);
//         }
//     } catch (e) {
//         console.log(e);
//         return await responseError("ERROR :: " + (e.message || e), 407); // ส่งคืนข้อความผิดพลาด
//     }
// }

  public async sendLineNoti(req: any){
    try{
      let message = "Test\n";
      message += `📱 เทสไปเรื่อย\n`;

      console.log(message);
      
      const resLineNoti = await lineNotify("*******************************************",message); ///ส่ง line

      console.log(resLineNoti);
      
    }catch (e){
      console.log(e);
      
    }
  }

  public async sendNotiOnlineSpareParts(req: any) {
    try {
      const params = await req.data;
      const type = params.type;
      
      let text = "แจ้งสั่งซื้อสินค้าอะไหล่ประดับยนต์กับบริษัทประชากิจฯ\n";
      text += `คุณ ${params.name} ได้สั่งซื้อสินค้าอะไหล่ประดับยนต์กับบริษัทประชากิจฯ จำนวน ${params.qty} ชิ้น\n`;
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }
    
}

export let NotificationModule = new NotificationController();