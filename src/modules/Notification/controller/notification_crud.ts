import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";
import { TGNotify } from "../../../help/TG-notify";
import { moment } from "moment-timezone";



class Noticrud {

  public async getNotifyByRunning(req: any) {
    try {
      const params = await req.data;
      const res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT running, create_time, title, detail, link_url, body, phone, status_sign FROM notificaion_log WHERE running = ? AND status_show = ?`,
        [params.running, 'Y']
      );
      if (res != "") {
        return responseSuccess(res, 200);
      } else {
        return responseError("ERROR :: not found data ", 201);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }

  public async getNotify(req: any) {
    try {
      const params = await req.data;
      const res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT running, create_time, title, detail, link_url, body, phone, status_sign FROM notificaion_log WHERE phone = ? AND status_show = ? ORDER BY create_time DESC`,
        [params.phone, 'Y']
      );
      if (res != "") {
        return responseSuccess(res, 200);
      } else {
        return responseError("ERROR :: not found data ", 201);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }

  public async saveNotify(req: any) {
    try {
      const params = await req.data;
      const res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `INSERT INTO notificaion_log (title, detail, link_url, phone, body, status_show) VALUES (?, ?, ?, ?, ?, ?)`,
        [params.title, params.detail, params.url, params.phone, params.body, "Y"]
      );
      console.log(res);

      if (res != false) {
        return responseSuccess({ "lastInsertId": res.lastInsertId }, 200);
      } else {
        return responseError("ERROR :: not inserted data ", 204);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }

  public async updateNotify(req: any) {
    try {
      const params = await req.data;
      const show = params.status_show ?? "y"
      const res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `UPDATE notificaion_log SET title = ?, detail = ?, link_url = ?, body = ?, status_show = ?, status_sign = ? WHERE phone = ?`,
        [params.title, params.detail, params.url, params.body, show, params.status_sign, params.phone]
      );
      if (res != false) {
        return responseSuccess("message :: update success", 200);
      } else {
        return responseError("ERROR :: not updated data ", 204);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }

  public async updateNotifyFollowRunning(req: any) {
    try {
      const params = await req.data;
      const show = params.status_show ?? "y"

      const now = new Date(Date.now()).toLocaleString('th-TH', {
        timeZone: 'Asia/Bangkok',
      });

      // return bodyData;
      const res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `UPDATE notificaion_log SET title = ?, detail = ?, link_url = ?, body = ?, status_show = ?, status_sign = ? WHERE running = ?`,
        [params.title, params.detail, params.url, params.body, show, params.status_sign, params.running]
      );
      if (res != false) {
        // ลูกค้าเซ็นต์เอกสารแล้ว
        const properJsonString = params.body.replace(/'/g, '"');
        const convertCode = JSON.parse(properJsonString);
        const textArray = convertCode.detailText.split('-n');

        const mergedText = textArray.filter(element => element !== "-d").join('\n');
        const nameCollect = textArray[0].split(':');


        const bodyData = {
          topicId: "0.0.6335677",
          data: {
              create_time: now,
              customer_name: nameCollect[1],
              phone: params.phone,
              mr_code:"",
              type: params.title,
              running: params.running,
              url:params.status_sign,
              accept: "Yes"
          }
        };

          const newRequest = new Request(hcsURL, {
              body: JSON.stringify(bodyData),
              headers: {
              "Content-Type": "application/json",
              "x-api-key": hcsX
              },
              method: "POST",
          });
          fetch(newRequest);

          const notiAdminRequest = new Request("https://devdev.prachakij.com/PMS/SIGN/SA/service_after_sign_in_app.php?running="+params.running, {
            headers: {
            "Content-Type": "application/json",
            },
            method: "GET",
        });
          fetch(notiAdminRequest);

        if(params.title == "เอกสารใบจองรถใหม่"){
          await TGNotify("ลูกค้าเซ็นต์เอกสารแล้ว\n"+params.title+"\n"+mergedText, "-4177988451");
        }else {
          await TGNotify("ลูกค้าเซ็นต์เอกสารแล้ว\n"+params.title+"\n"+mergedText, "-1002120594897");
        }

        return responseSuccess("message :: update success", 200);
      } else {
        return responseError("ERROR :: not updated data ", 204);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }

  public async deleteNotifyFollowRunning(req: any) {
    try {
      const params = await req.data;
      const show = params.status_show ?? "y"
      const res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `UPDATE notificaion_log SET status_show = ? WHERE running = ?`,
        [show, params.running]
      );
      if (res != false) {
        return responseSuccess("message :: update success", 200);
      } else {
        return responseError("ERROR :: not updated data ", 204);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }

  public async deleteNotifyAll(req: any) {
    try {
      const params = await req.data;
      const res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `UPDATE notificaion_log SET status_show = ? WHERE phone = ?`,
        ["N", params.phone]
      );
      if (res != false) {

        return responseSuccess("message :: delete success", 200);
      } else {
        return responseError("ERROR :: not updated data ", 204);
      }
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: " + e, 407);
    }
  }
}

export const Notifycrud = new Noticrud();