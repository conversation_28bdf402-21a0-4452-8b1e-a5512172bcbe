import Joi, { any } from "joi"
import { responseError } from "../../../help/response";

export class ValidateNotification {

    public async ValidateSendNotification(req: any) {
        try {
            const params = await req.data;
            console.log(params);
            console.log("=-=-=-=-=-=-=");

            let schema = Joi.object({
                running: Joi.any(),
                phone: Joi.string(),
                detail: Joi.any(),
                title: Joi.any(),
                note: Joi.any(),
                url: Joi.any(),
                type: Joi.any(),
                body: Joi.any(),
                status_show: Joi.any(),
                status_sign: Joi.any()
            });

            const { error } = await schema.validate(params);

            console.log(error);
            console.log("=-=-=-=-=-=-=");
            if (error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async ValidateSendNotificationByPass(req: any) {
        try {
            const params = await req.data;
            console.log(params);
            console.log("=-=-=-=-=-=-=");

            let schema = Joi.object({
                running: Joi.any(),
                phone: Joi.string().required(),
                detail: Joi.any().required(),
                title: Joi.any().required(),
                url: Joi.any().required(),
                type: Joi.any(),
                body: Joi.any().required()
            });
 
            const { error } = await schema.validate(params);

            console.log(error);
            console.log("=-=-=-=-=-=-=");
            if (error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationNotification = new ValidateNotification();