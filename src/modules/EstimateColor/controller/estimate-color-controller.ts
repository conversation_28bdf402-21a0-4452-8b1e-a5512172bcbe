import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class EstimateColorController {
    // TODO :: GET Estimate Color
    public async getEstimateColor(req: any) {
        try {
            let bodyData = await req.data;
            let color: { [key: string]: string } = {
                "สีธรรมดา": "Solid",
                "สีเมทาลิก": "Metallic",
                "สีมุก": "3 Coat Pearl",
            };
            
            if (bodyData.type_color in color) {
                bodyData.type_color = color[bodyData.type_color];
            }

            let value = {
                type_color: bodyData.type_color,
                generation: bodyData.generation,
                description: bodyData.description.map((desc: string) => `'${desc.trim()}'`).join(","),
                degree: "เบา",
            };

            // console.log(value.description);

            let res = await connectdocker.connect(
                DatabaseConfig.BCT_PMG_NEW,
                `SELECT * , SUM(lapor_price) AS total_labor_price
                FROM labor_cost
                WHERE generation = ?
                  AND color = ?
                  AND degree = ?
                  AND description IN (${value.description})`,
                [value.generation, value.type_color, value.degree]
            );
            // console.log(res);

            return await responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError("ERROR :: Get Estimate Color", 407);
        }
    }
}
export let EstimateColorModule = new EstimateColorController();