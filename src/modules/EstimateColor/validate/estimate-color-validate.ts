import Jo<PERSON> from "joi";
import {responseError} from "../../../help/response";

export class ValidateEstimateColor {
    public async ValidateEstimateColor(req: any){
        try {
            // console.log(`req.data ==>> ${req.data}`);
            const params = await req.data;
            // console.log(params);
            let schema = Joi.object({
                type_color: Joi.string().required(),
                generation: Joi.string().required(),
                description: Joi.array().required(),
            })

            const {error} = schema.validate(params);
            // console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
            
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationEstimateColor = new ValidateEstimateColor();