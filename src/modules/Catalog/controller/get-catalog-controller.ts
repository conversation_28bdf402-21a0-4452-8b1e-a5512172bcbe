import { Request } from "itty-router";
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";

class GetCatalogController {

    public async getCatalog(req: any) {
        try {
            console.log('router getCatalog');
            
            var res = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT catalog_title, catalog_text, catalog_image, catalog_url, catalog_order FROM car_catalog ORDER BY catalog_order`,
            []
          )
          console.log(typeof res);
          
          return responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Get Catalog ${e}`, 407);
        }
    }
}

export let GetCatalogModule = new GetCatalogController();