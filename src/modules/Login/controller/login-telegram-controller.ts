import { connectdocker } from "help/connectDataBase";
import {responseError, responseSuccess} from "../../../help/response";
import { DatabaseConfig } from "help/database.enum";

class LoginTG {
    public async loginTG(req: any) {
        try {
            const params = await req.data;
            console.log(params);
            
            console.log(params.userID_tg);
            
            const res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT id, roleId, mobile
               FROM customercenter
               WHERE userID_tg = ? LIMIT 1`,
                [params.userID_tg]
            )
            if (res.length > 0) {
                return responseSuccess(res[0], 200);
            } else {
                return responseError("ERROR :: Get login tg", 407);
            }

        } catch (e) {
            console.log(e);
            return await responseError("ERROR :: Get login tg", 407);
        }
    }
}

export let LoginTGModule = new LoginTG();