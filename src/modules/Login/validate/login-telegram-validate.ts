import { responseError } from "help/response";
import <PERSON><PERSON> from "joi";

export class ValidateLoginTG {
    public async ValidateLoginTG(req: any) {
        try {
            const params = await req.params;
            let schema = Joi.object({
                userID_tg: Joi.string()
            });

            const {error} = await schema.validate(params);

            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidateLoginTGModule = new ValidateLoginTG();