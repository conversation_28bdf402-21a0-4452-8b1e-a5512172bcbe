import { Request } from "itty-router";
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import {AncillaryProductType} from "../type/ancillary-product-type";

class AncillaryProductController {
  public async getAllAncillaryProduct(req: any) {
    console.log(await req.data);

    const res = await connectdocker.connect(
      DatabaseConfig.webPKG,
      "SELECT * FROM ancillary_product",
      []
    ) as AncillaryProductType[]
    return responseSuccess(res, 200);
  }



  public async createAncillaryProduct(req: any) {
    try {
      let bodyData = await req.data;
      
      let value = {
        product_name: bodyData.product_name.toString().trim(),
        sell_price: bodyData.sell_price.toString().trim(),
        cost_price: bodyData.cost_price.toString().trim(),
        profit_price: bodyData.profit_price.toString().trim(),
        barcode: bodyData.barcode.toString().trim(),
        create_user: bodyData.email,
      }
      
      let res =  await connectdocker.connect(
          DatabaseConfig.webPKG,
          "INSERT INTO ancillary_product (product_name, sell_price, cost_price, profit_price, barcode, create_user) VALUES (?, ?, ?, ?, ?, ?)",
          [value.product_name, value.sell_price, value.cost_price, value.profit_price, value.barcode, value.create_user]
      )
      return await responseSuccess(res, 200);
    }catch (e) {
      console.log(e)
      return await responseError("ERROR :: Create Ancillary Product", 407);
    }
  }

  
}

export let AncillaryProductModule = new AncillaryProductController();