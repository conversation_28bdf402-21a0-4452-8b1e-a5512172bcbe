import Joi from "joi";
import {Request} from "itty-router";
import {responseError} from "../../../help/response";


export class ValidateAncillaryProduct {

    public async ValidateCreateAncillaryProduct(req: any) {
        try {

            const params = await req.data;
            let schema = Joi.object({
                product_name: Joi.string().required(),
                sell_price: Joi.string().required(),
                cost_price: Joi.number().required(),
                profit_price: Joi.number().required(),
                barcode: Joi.string().required(),
                create_user: Joi.string().required(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationAncillaryProduct = new ValidateAncillaryProduct();

