import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class CreateNewsController {
    public async createNews(req: any) {
        try {
            const bodyData = await req.data;
            // const queryString = INSERT INTO ${table} (app_name, yubi_name, yubi_Id, email, client_secret, client_id) VALUES (?, ?, ?, ?, ?, ?)
            let value = {
                user_create_id: bodyData.user_create_id,
                information_name: bodyData.information_name,
                information_body: bodyData.information_body,
                information_pic: bodyData.information_pic,
                information_date: bodyData.information_date,
                information_date_end: bodyData.information_date_end,
                information_link: bodyData.information_link,
                information_flag: bodyData.information_flag,
            }
            var res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                // `INSERT INTO tbl_information SET ?`,
                `INSERT INTO tbl_information (user_create_id, information_name, information_body, information_pic, information_date, information_date_end, information_link, information_flag) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [value.user_create_id, value.information_name, value.information_body, value.information_pic, value.information_date, value.information_date_end, value.information_link, value.information_flag]
            )
            return await responseSuccess(res, 200);
        } catch (e){
            return await responseError(`ERROR :: Create News => ${e}`, 407);      
        }
    }
}

export let createNewsModule = new CreateNewsController();