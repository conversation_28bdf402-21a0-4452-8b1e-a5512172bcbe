import { Request } from "itty-router";
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";

class GetNewsByIDController {

    public async getNewsByID(req: any) {
        try {
            console.log('router getNewsByID');
            let params = await req.params;
            
            var res = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT information_id, information_name, information_body, information_pic, information_date,information_link FROM tbl_information WHERE information_id = ? AND information_flag = 1`,
            [params.id]
          )
          console.log(typeof res);
          
          return responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Get News By ID ${e}`, 407);
        }
    }
}

export let GetNewsByIDModule = new GetNewsByIDController();