import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class UpdateNewsController {
    public async updateNews(req: any) {
        try {
            const bodyData = await req.data;
            let value = {
                user_edit_id: bodyData.user_edit_id,
                information_name: bodyData.information_name,
                information_body: bodyData.information_body,
                information_pic: bodyData.information_pic,
                information_date: bodyData.information_date,
                information_date_end: bodyData.information_date_end,
                information_link: bodyData.information_link,
                information_flag: bodyData.information_flag,
            } 
            var res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `UPDATE tbl_information SET user_edit_id = ?, information_name = ?, information_body = ?, information_pic = ?, information_date = ?, information_date_end = ?, information_link = ?, information_flag = ? WHERE information_id = ?`,
                [value.user_edit_id, value.information_name, value.information_body, value.information_pic, value.information_date, value.information_date_end, value.information_link, value.information_flag, bodyData.information_id]
            )
            return responseSuccess(res, 200);
        } catch (e){
            return await responseError(`ERROR :: Update News => ${e}`, 407); 
        }
    }
}

export let updateNewsModule = new UpdateNewsController();