import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class DeleteNewsController {
    public async deleteNews(req: any) {
        try {
            const bodyData = await req.data;
            let value = {
                user_edit_id: bodyData.user_edit_id,
                information_flag: bodyData.information_flag,
            } 
            var res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `UPDATE tbl_information SET user_edit_id = ?, information_flag = ? WHERE information_id = ?`,
                [value.user_edit_id, value.information_flag, bodyData.information_id]
            )
            return responseSuccess(res, 200);
        } catch (e){
            return await responseError(`ERROR :: Delete News => ${e}`, 407); 
        }
    }
}

export let deleteNewsModule = new DeleteNewsController();