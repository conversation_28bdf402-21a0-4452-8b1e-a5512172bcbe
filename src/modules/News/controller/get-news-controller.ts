import { responseError, responseSuccess } from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";

class GetNewsController {
  public async getNews(req: any) {
    try {
      console.log("router getNews");

      var res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT
            information_id,
            information_name,
            information_body,
            information_pic,
            information_date,
            information_date_end,
            information_link
        FROM
            tbl_information
        WHERE
            information_flag = 1 AND information_date_end >= DATE(NOW())
        ORDER BY
            information_id
        DESC
            `,
        []
      );

      // AND NOW() NOT BETWEEN a.fdate_regis_activity AND a.edate_activity

      return responseSuccess(res, 200);
    } catch (e) {
      console.log(e);
      return await responseError(`ERROR :: Get News ${e}`, 407);
    }
  }

  public async getNewsAll(req: any) {
    try {
      console.log("router getNewsAll");

      var res = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT i.information_id, i.information_name, i.information_body, i.information_pic, i.information_date,i.information_date_end, i.information_link
        FROM tbl_information i
        LEFT JOIN id_activity a ON i.information_id = a.information_id 
        WHERE i.information_flag = 1 
        ORDER BY i.information_id DESC 
        LIMIT 20
        `,
        []
      );

      return responseSuccess(res, 200);
    } catch (e) {
      console.log(e);
      return await responseError(`ERROR :: Get News All ${e}`, 407);
    }
  }
}

export let GetNewsModule = new GetNewsController();
