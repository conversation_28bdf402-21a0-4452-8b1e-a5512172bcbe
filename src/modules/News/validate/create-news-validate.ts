import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateCreateNews {
    public async ValidateCreateNews(req: any) {
        try {
            const params = await req.data;
            
            let schemaGet = Joi.object({
                user_create_id: Joi.any().required(),
                information_name: Joi.any(),
                information_body: Joi.any(),
                information_pic: Joi.any(),
                information_date: Joi.any(),
                information_date_end: Joi.any(),
                information_link: Joi.any(),
                information_flag: Joi.any(),
            });

            const {error} = await schemaGet.validate(params);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationCreateNews = new ValidateCreateNews();