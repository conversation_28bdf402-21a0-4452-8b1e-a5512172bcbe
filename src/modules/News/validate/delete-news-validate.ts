import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateDeleteNews {
    public async ValidateDeleteNews(req: any) {
        try {
            const params = await req.data;
            
            let schema = Joi.object({
                information_id: Joi.required(),
                user_edit_id: Joi.any().required(),
                information_flag: Joi.any(),
            });

            const {error} = await schema.validate(params);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationDeleteNews = new ValidateDeleteNews();