import { responseError } from "help/response";
import <PERSON><PERSON> from "joi";

export class ValidateRegisterTG {

    public async ValidateRegisterTG(req: any) {
        try {
            const params = await req.data;

            let schema = Joi.object({
                userID_tg: Joi.string(),
                mobile: Joi.string(),
                roleId: Joi.string(),
                fullname: Joi.string(),
                firstname: Joi.string(),
                lastname: Joi.string(),
                create_user: Joi.string()
            })

            const {error} = await schema.validate(params);

            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    };

    public async ValidateUpdateIDTG(req: any) {
        try {
            const params = await req.data;

            let schema = Joi.object({
                userID_tg: Joi.string(),
                id: Joi.string(),
            })

            const {error} = await schema.validate(params);

            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    };
}

export let ValidateRegisterTGModule = new ValidateRegisterTG();