import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateRegister {
    
    public async ValidateRegister(req: any) {
        try {
            const params = await req.data;

            let schema = Joi.object({
                phone : Joi.string().required(),
                firstName : Joi.string().required(),
                lastName : Joi.string().required(),
                phoneFirebase : Joi.string().required(),
                displayName : Joi.string().required(),
                refcode : Joi.string().allow('', null),
            })

            const {error} = await schema.validate(params);
            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    };


    public async ValidateRegisterSocial(req: any) {
        try {
            const params = await req.data;

            let schema = Joi.object({
                typeConnect: Joi.string().valid('line', 'apple').required(), // "typeRegis" changed to "typeConnect"
                userID: Joi.string().required(), // Assuming userID should be a required string
                phone: Joi.string().required(), // Corresponds to the original "mobile" field
                phoneFirebase: Joi.string().required(), // Corresponds to the original "phone_firebase" field
                refcode: Joi.string().allow('', null), // Remains the same
                displayName: Joi.string().required(), // Assuming displayName should be a required string
                firstName: Joi.string().required(), // Corresponds to the original "firstname" field
                lastName: Joi.string().required(), // Correspng and null
            });

            const {error} = await schema.validate(params);
            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    };



}

export let ValidationRegister = new ValidateRegister();