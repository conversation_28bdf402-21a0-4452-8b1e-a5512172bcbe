import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { hashPass, compareSync } from "../../../help/hash";
import { responseError, responseSuccess } from "../../../help/response";
import { LockLikeModule } from "../../../modules/LockLike/controller/lock-like";

class RegisterController {

  public async register(req: any) {
    try {
      let bodyData = await req.data;

      //TODO :: Check User
      const resCheckuser = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT username
         FROM customercenter
         WHERE mobile = ?`,
        [bodyData.mobile]
      );
      if (resCheckuser.length != 0) {
        return await responseError("Phone number is using", 202);
      }
      //TODO :: Check User

      // TODO :: Register
      let value = {
        create_user: "Mapp",
        mobile: bodyData.phone,
        firstname: bodyData.firstName,
        lastname: bodyData.lastName,
        fullname: bodyData.displayName,
        phone_firebase: bodyData.phoneFirebase,
        advisor_id: bodyData.refcode ?? ""
      };
      var columns = Object.keys(value).join(",");
      var placeholders = Object.keys(value).map(() => "?").join(",");
      var values = Object.values(value);
      var sqlQuery = `INSERT INTO customercenter (${columns})
                      VALUES (${placeholders})`;
      var dataInsert = await connectdocker.connect(DatabaseConfig.webPKG, sqlQuery, values);
      if (dataInsert.affectedRows === 0) {
        return await responseError("ERROR :: Register Social", 407);
      } else {

        var bodyPoi = {
          phone: bodyData.phoneFirebase,
          activityID: KEY_POI_DOWNLOAD,
          firstName: bodyData.firstName,
          lastName: bodyData.lastName,
          merchantID: MERCHANT_ID
        };
        const newRequest = new Request(`${API_URL_LIKEPOINT}/transactions-activity/pay-poi-in-app`, {
          body: JSON.stringify(bodyPoi),
          headers: {
            "Content-Type": "application/json",
            "x-api-key": API_KEY_LIKEPOINT
          },
          method: "POST"
        });
        const res = await fetch(newRequest);
        const resSaveActivity = await res.json();

        if (resSaveActivity.data[1].statusPay == "Completed") {
          return await responseSuccess({
            memberID: dataInsert.lastInsertId,
            resSaveActivity: resSaveActivity.data[1]
          }, 200);
        }
      }
      // TODO :: Register

    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: Register", 407);
    }
  }

  public async login(req: any) {
    let bodyData = await req.data;
    console.log(bodyData);
    const resCheckuser = await connectdocker.connect(
      DatabaseConfig.webPKG,
      `SELECT password
       FROM customercenter
       WHERE mobile = ?`,
      ["0647832303"]
    );
    console.log(resCheckuser[0]["password"]);

    const passEd = await compareSync(bodyData.password, resCheckuser[0]["password"]);
    console.log(passEd);
  }


  public async registerWithRefcode (req: any){

    try {
      let bodyData = await req.data;

      //TODO :: Check User
      const resCheckuser = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT username
         FROM customercenter
         WHERE mobile = ?`,
        [bodyData.mobile]
      );
      if (resCheckuser.length != 0) {
        return await responseError("Phone number is using", 202);
      }
      //TODO :: Check User

      // TODO :: Register
      let value = {
        create_user: "Mapp",
        mobile: bodyData.phone,
        firstname: bodyData.firstName,
        lastname: bodyData.lastName,
        fullname: bodyData.displayName,
        phone_firebase: bodyData.phoneFirebase,
        ref_code_mr: (!bodyData.refcode || bodyData.refcode.trim() === "MR-") ? null : bodyData.refcode
      };
      
      
      var columns = Object.keys(value).join(",");
      var placeholders = Object.keys(value).map(() => "?").join(",");
      var values = Object.values(value);
      var sqlQuery = `INSERT INTO customercenter (${columns})
                      VALUES (${placeholders})`;
      var dataInsert = await connectdocker.connect(DatabaseConfig.webPKG, sqlQuery, values);
      if (dataInsert.affectedRows === 0) {
        return await responseError("ERROR :: Register Social", 407);
      } else {
        var bodyPoi = {
          phone: bodyData.phoneFirebase,
          activityID: KEY_POI_DOWNLOAD,
          firstName: bodyData.firstName,
          lastName: bodyData.lastName,
          merchantID: MERCHANT_ID
        };
        const newRequest = new Request(`${API_URL_LIKEPOINT}/transactions-activity/pay-poi-in-app`, {
          body: JSON.stringify(bodyPoi),
          headers: {
            "Content-Type": "application/json",
            "x-api-key": API_KEY_LIKEPOINT
          },
          method: "POST"
        });
        const res = await fetch(newRequest);
        const resSaveActivity = await res.json();

        if (resSaveActivity.data[1].statusPay == "Completed") {
          return await responseSuccess({
            memberID: dataInsert.lastInsertId,
            resSaveActivity: resSaveActivity.data[1]
          }, 200);
        }
      }
      // TODO :: Register

    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: Register", 407);
    }
    

  }

}

export let registerModule = new RegisterController();