import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { fetchRequestService } from "help/fetchButTS";
import { LikeWalletConfig } from "help/likewallet.enum";
import { responseError, responseSuccess } from "help/response";
import { LockLikeModule } from "modules/LockLike/controller/lock-like";

class registerTG {
    public async registerTG(req: any) {
        try {
            const params = await req.data;

            var fullName = params.firstname+" "+params.lastname;
            var phoneFirebase = params.mobile.replace(/^0/, '+66');
            const res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `INSERT INTO customercenter (userID_tg, mobile, roleId, fullname, firstname, lastname, create_user, phone_firebase) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [params.userID_tg, params.mobile, params.roleId, fullName, params.firstname, params.lastname, params.create_user, phoneFirebase]
            )
            ///สร้างกระเป๋า

            var resDataLike = await fetchRequestService.post(
                LikeWalletConfig.API_URL_CREATE_WALLET,
                {
                    "apikey": LikeWalletConfig.API_KEYLIKE,
                    "secret": LikeWalletConfig.SECRET_KEYLIKE,
                    "firstname": params.firstname,
                    "lastname": params.lastname,
                    "phone_number": phoneFirebase,
                    "application": "PMS"
                 },
                {
                    "X-Api-Key": LikeWalletConfig.X_API_KEY
                }
              );
              
              if(resDataLike.statusCode == 204){
                ///มีกระเป๋าแล้ว
                var resAddress = await LockLikeModule.getBalanceByphoneNumber(phoneFirebase);
                var walletID = resAddress["result"]["address"];
                var act_id = 577;
                ///จ่าย Like
                await LockLikeModule.insertTransactionPOI(params.mobile, null, "สมัครสมาชิกใหม่", act_id);
                var resPayLike = await LockLikeModule.payLike(params.mobile, act_id, "");
              } else if(resDataLike.statusCode == 200){
                ///สร้างกระเป๋า
                var walletID = resDataLike.data.address;
                ///จ่าย Like
                var act_id = 577;
                await LockLikeModule.insertTransactionPOI(params.mobile, null, "สมัครสมาชิกใหม่", act_id);
                var resPayLike = await LockLikeModule.payLike(params.mobile, act_id, "");
              } else {
                console.log('สร้างกระเป๋าไม่สำเร็จ', resDataLike);
              }

            return await responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError("ERROR :: Get register tg", 407);
        }
    }

    public async updateIDTG(req: any) {
        try {
            const params = await req.data;
            console.log(params);
            const res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `UPDATE customercenter SET userID_tg = ? WHERE id = ?`,
                [params.userID_tg, params.id]
            )
            if (res.affectedRows === 0) {
                return await responseError("ID Not found", 404);
            }

            return await responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError("ERROR :: Get register tg", 407);
        }
    }

}

export let registerTGModule = new registerTG();