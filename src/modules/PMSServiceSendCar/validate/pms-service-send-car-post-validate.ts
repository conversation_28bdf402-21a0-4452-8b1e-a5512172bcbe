import Joi, { any } from "joi";
import { responseError } from "../../../help/response";

export class ValidatePMSServiceSendCarPost {
  public async ValidateSendPMSServiceSendCarPost(req: any) {
    try {
      const params = await req.data;
      let schema = Joi.object({
        engin: Joi.string(),
      });
      const { error } = await schema.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }
}

export let ValidationPMSServiceSendCarPost =
  new ValidatePMSServiceSendCarPost();
