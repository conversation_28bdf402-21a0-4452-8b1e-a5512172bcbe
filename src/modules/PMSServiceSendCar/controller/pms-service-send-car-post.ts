import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class PMSServiceSendCarGet {
  public async getSummarySheet(req: any) {
    try {
      let bodyData = await req.data;
      let res = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT l.running, l.rono, l.vehiclelicenseplate, l.engineno, ms.url_file_cust, ms.url_file_receipt, ms.pay_total
        FROM ListofRepairOrder_DotMatrix_th l
        LEFT JOIN MIRAI_SUMMARY_SIGN ms
        ON l.rono = ms.ref_id
        WHERE engineno = ? 
        ORDER BY running DESC`,
        [bodyData.engin]
      );
      if (res.length === 0) {
        return await responseError("getSummarySheet not found", 404);
      }
      return await responseSuccess(res[0], 200);
    } catch (error) {
      console.log(error);
      await responseError(error, 407);
    }
  }
}

export let PMSServiceSendCarPostModule = new PMSServiceSendCarGet();
