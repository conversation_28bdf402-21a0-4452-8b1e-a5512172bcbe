import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class NewCarController {
    
    public async getNewCar(req: any) {
        try {
          let bodyData = await req.data;
          const formattedPhoneNumber = `${bodyData.phone.substring(0, 3)}-${bodyData.phone.substring(3)}`;

          const newCarList: any[] = [];
          const sqlBooking = `SELECT MAX(bookingNo) AS bookingNo, carCode, car_model_sa, car_model, colorCode, colorName, bookingDate, finan_approve, Status_match, bookingStatus, Advisor, centerPhone, centerName FROM BCT_BOOKING WHERE centerPhone IN (?) AND bookingStatus != "ยกเลิก" AND bookingStatus != "ยกเลิกจอง" AND YEAR(bookingDate) >= YEAR(CURDATE()) - 2 GROUP BY carCode`
          // TODO :: call booking
          let resBooking = await connectdocker.connect(
            DatabaseConfig.BCT_AMS1,
            sqlBooking,
            [[formattedPhoneNumber]]
          );

          if (resBooking.length === 0) {
            return await responseSuccess({ status: "no car reservation" }, 411);
          }

          // TODO :: Check Car Booking
          for (const car of resBooking) {
            newCarList.push({statusApp: "book an appointment", bookingNo: car.bookingNo, car: car.car_model, car_code: car.carCode, car_type: car.car_model_sa, color_code: car.colorCode, color: car.colorName, bookingStatus: car.bookingStatus, finance: car.finan_approve, status_match_booking: car.Status_match, advisor: car.Advisor, centerPhone: car.centerPhone, centerName: car.centerName});
          }

          for (const car of newCarList) {
            // TODO :: get car picture
            
            let resCarPic = await connectdocker.connect(
              DatabaseConfig.webPKG,
              `SELECT *
               FROM CarForPKG
               LEFT JOIN CarColorForPKG ON CarForPKG.car_id = CarColorForPKG.car_id
               LEFT JOIN CarImageForPKG ON CarColorForPKG.color_id = CarImageForPKG.color_id
               WHERE CarForPKG.type_car = ? AND CarColorForPKG.name = ?`,
              [car.car_code, car.color_code]
            );

            console.log("resCarPic", resCarPic);
          
            if (resCarPic.length === 0) {
              car.car_image = "";
            } else {
              car.car_image = resCarPic[0].image_url ?? "";
            }
            // TODO :: get car picture

            // TODO :: getSC
            let resSC = await connectdocker.connect(
              DatabaseConfig.webPKG,
              `SELECT sale_nickname, sale_picture, sale_phone FROM BCT_sale WHERE sale_name LIKE ?`,
              [`%${car.advisor}%`]
            );
            
              car.advisorNickname = resSC[0].sale_nickname ?? "",
              car.advisorPic = resSC[0].sale_picture ?? "",
              car.advisorPhone = resSC[0].sale_phone ?? ""

            // TODO :: Check Car Finance  
            if (car.finance === null || car.finance === "") {
              car.statusApp = "waiting finance";
            } else if (car.finance === "อนุมัติ") {
              car.statusApp = "finance approve";
            } else if (car.finance === "ไม่อนุมัติ") {
              car.statusApp = "finance not approve";
            }

            // TODO :: Check Car Stock
            let resCarStock = await connectdocker.connect(
              DatabaseConfig.BCT_AMS1,
              `SELECT bookingNo, engineCode, Status_match, car_to_headoffice FROM BCT_CAR_STOCK WHERE bookingNo = ?`,
              [car.bookingNo]
            );

            if (resCarStock.length > 0) {
              car.status_match_car_stock = resCarStock[0]?.Status_match;
              car.engineCode = resCarStock[0].engineCode;
              car.car_to_headoffice = resCarStock[0].car_to_headoffice;
            
              // TODO :: Check Car Match
              if (car.status_match_booking === car.status_match_car_stock) {
                car.statusApp = "have car in stock";
              }
              
              if (car.car_to_headoffice !== "0000-00-00") {
                car.statusApp = "car to headoffice";
              }
            }
            
            // TODO :: Check Car Custom
            let resCarCustom = await connectdocker.connect(
              DatabaseConfig.BCT_AMS1,
              `SELECT bookingNo, create_time FROM BCT_order_accressory WHERE bookingNo = ?`,
              [car.bookingNo]
            );
            

            if (resCarCustom.length > 0) {
              car.statusApp = "custom car"
            }

            // TODO :: Check Car Delivery
            let resCarDelivery = await connectdocker.connect(
              DatabaseConfig.BCT_AMS1,
              `SELECT ctt_date FROM BCT_CONTRACT WHERE bookingNo = ? AND (cancle IS NULL OR cancle = "")`,
              [car.bookingNo]
            );

            if (resCarDelivery.length > 0) {
              car.statusApp = "delivery car"
            }

            // TODO :: Check Car Finish
            let resFinishNewCar = await connectdocker.connect(
              DatabaseConfig.MIRAI,
              `SELECT running, Column90 FROM CVIPMirai WHERE Column100 = ? ORDER BY running DESC LIMIT 1`,
              [car.engineCode]
            );
             
            if (resFinishNewCar.length > 0 && resFinishNewCar[0]?.Column90 !== '0000-00-00') {
              car.statusApp = "successfully";
            }
          }

          console.log("out END");
          return await responseSuccess({newCarList}, 200);

        } catch (e) {
          console.log("Error:", e);
          return await responseError("ERROR :: Create Appointment", 407);
        }
    }

    public async getNewCarWithBookingNo(req: any) {
      
      try {
        let bodyData = await req.data;
        
        const newCarList: any[] = [];
        const sqlBooking = `SELECT MAX(bookingNo) AS bookingNo, carCode, car_model_sa, car_model, colorCode, colorName, bookingDate, finan_approve, Status_match, bookingStatus, Advisor, centerPhone, centerName FROM BCT_BOOKING WHERE bookingNo = ? AND bookingStatus != "ยกเลิก" AND bookingStatus != "ยกเลิกจอง" AND YEAR(bookingDate) >= YEAR(CURDATE()) - 2 GROUP BY carCode`
        // TODO :: call booking
        let resBooking = await connectdocker.connect(
          DatabaseConfig.BCT_AMS1,
          sqlBooking,
          [bodyData.bookingNo]
        );

        if (resBooking.length === 0) {
          return await responseSuccess({ status: "no car reservation" }, 411);
        }

        // TODO :: Check Car Booking
        for (const car of resBooking) {
          newCarList.push({statusApp: "book an appointment", bookingNo: car.bookingNo, car: car.car_model, car_code: car.carCode, car_type: car.car_model_sa, color_code: car.colorCode, color: car.colorName, bookingStatus: car.bookingStatus, finance: car.finan_approve, status_match_booking: car.Status_match, advisor: car.Advisor, centerPhone: car.centerPhone, centerName: car.centerName});
        }

        for (const car of newCarList) {
          // TODO :: get car picture
          
          let resCarPic = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT *
             FROM CarForPKG
             LEFT JOIN CarColorForPKG ON CarForPKG.car_id = CarColorForPKG.car_id
             LEFT JOIN CarImageForPKG ON CarColorForPKG.color_id = CarImageForPKG.color_id
             WHERE CarForPKG.type_car = ? AND CarColorForPKG.name = ?`,
            [car.car_code, car.color_code]
          );
          if (resCarPic.length === 0) {
            car.car_image = "";
          } else {
            car.car_image = resCarPic[0].image_url ?? "";
          }

           
          // TODO :: get car picture

          // TODO :: getSC
          let resSC = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT sale_nickname, sale_picture, sale_phone FROM BCT_sale WHERE sale_name LIKE ?`,
            [`%${car.advisor}%`]
          );
          
            car.advisorNickname = resSC[0].sale_nickname ?? "",
            car.advisorPic = resSC[0].sale_picture ?? "",
            car.advisorPhone = resSC[0].sale_phone ?? ""

          // TODO :: Check Car Finance  
          if (car.finance === null || car.finance === "") {
            car.statusApp = "waiting finance";
          } else if (car.finance === "อนุมัติ") {
            car.statusApp = "finance approve";
          } else if (car.finance === "ไม่อนุมัติ") {
            car.statusApp = "finance not approve";
          }

          // TODO :: Check Car Stock
          let resCarStock = await connectdocker.connect(
            DatabaseConfig.BCT_AMS1,
            `SELECT bookingNo, engineCode, Status_match, car_to_headoffice FROM BCT_CAR_STOCK WHERE bookingNo = ?`,
            [car.bookingNo]
          );

          if (resCarStock.length > 0) {
            car.status_match_car_stock = resCarStock[0]?.Status_match;
            car.engineCode = resCarStock[0].engineCode;
            car.car_to_headoffice = resCarStock[0].car_to_headoffice;
          
            // TODO :: Check Car Match
            if (car.status_match_booking === car.status_match_car_stock) {
              car.statusApp = "have car in stock";
            }
            
            if (car.car_to_headoffice !== "0000-00-00") {
              car.statusApp = "car to headoffice";
            }
          }
          
          // TODO :: Check Car Custom
          let resCarCustom = await connectdocker.connect(
            DatabaseConfig.BCT_AMS1,
            `SELECT bookingNo, create_time FROM BCT_order_accressory WHERE bookingNo = ?`,
            [car.bookingNo]
          );
          

          if (resCarCustom.length > 0) {
            car.statusApp = "custom car"
          }

          // TODO :: Check Car Delivery
          let resCarDelivery = await connectdocker.connect(
            DatabaseConfig.BCT_AMS1,
            `SELECT ctt_date FROM BCT_CONTRACT WHERE bookingNo = ? AND (cancle IS NULL OR cancle = "")`,
            [car.bookingNo]
          );

          if (resCarDelivery.length > 0) {
            car.statusApp = "delivery car"
          }

          // TODO :: Check Car Finish
          let resFinishNewCar = await connectdocker.connect(
            DatabaseConfig.MIRAI,
            `SELECT running, Column90 FROM CVIPMirai WHERE Column100 = ? ORDER BY running DESC LIMIT 1`,
            [car.engineCode]
          );
           
          if (resFinishNewCar.length > 0 && resFinishNewCar[0]?.Column90 !== '0000-00-00') {
            car.statusApp = "successfully";
          }
        }

        console.log("out END");
        return await responseSuccess({newCarList}, 200);

      } catch (e) {
        console.log("Error:", e);
        return await responseError("ERROR :: Create Appointment", 407);
      }
  }
      
}

export let NewCarModule = new NewCarController();