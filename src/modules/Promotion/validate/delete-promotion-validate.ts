import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateDeletePromotion {
    public async ValidateDeletePromotion(req: any) {
        try {
            const params = await req.data;
            
            let schema = Joi.object({
                promotion_id: Joi.required(),
                user_edit_id: Joi.any().required(),
                promotion_flag: Joi.any(),
            });

            const {error} = await schema.validate(params);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationDeletePromotion = new ValidateDeletePromotion();