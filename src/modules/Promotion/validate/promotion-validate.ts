import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidatePronotion {
    
    public async ValidateCreatePronotion(req: any) {
        try {
            const params = await req.data;

            let schema = Joi.object({
                user_create_id: Joi.string(),
                promotion_name: Joi.string(),
                promotion_body: Joi.string(),
                promotion_pic: Joi.string(),
                promotion_date: Joi.string(),
                promotion_end: Joi.string(),
                promotion_flag: Joi.string(),
            });

            const {error} = await schema.validate(params);

            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async ValidateUpdatePronotion(req: any) {
        try {
            const params = await req.data;

            let schema = Joi.object({
                promotion_id: Joi.string(),
                user_edit_id: Jo<PERSON>.string(),
                promotion_name: Joi.string(),
                promotion_body: Jo<PERSON>.string(),
                promotion_pic: Joi.string(),
                promotion_date: Joi.string(),
                promotion_edit_date: Joi.string(),
                promotion_end: Joi.string(),
                promotion_edit_flag: Joi.string(),
                promotion_flag: Joi.string(),
            });

            
            const {error} = await schema.validate(params);

            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationPronotion = new ValidatePronotion();