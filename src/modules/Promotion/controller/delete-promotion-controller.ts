import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class DeletePromotionController {
    public async deletePromotion(req: any) {
        try {
            
            const bodyData = await req.data;
            console.log(bodyData);
            
            let value = {
                user_edit_id: bodyData.user_edit_id,
                promotion_flag: bodyData.promotion_flag,
                promotion_id: bodyData.promotion_id,
            } 
            
            var res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `UPDATE tbl_promotion SET user_edit_id = ?, promotion_flag = ? WHERE promotion_id = ?`,
                [value.user_edit_id, value.promotion_flag ,value.promotion_id]
            )

            return responseSuccess(res, 200);
        } catch (e){
            return await responseError(`ERROR :: Delete Promotion => ${e}`, 407); 
        }
    }
}

export let deletePromotionModule = new DeletePromotionController();