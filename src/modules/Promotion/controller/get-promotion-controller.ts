import { Request } from "itty-router";
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";

class GetPromotionController {

    public async getPromotion(req: any) {
        try {
            
            var res = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT *
                   FROM tbl_promotion
                   WHERE YEAR(promotion_end) >= YEAR(CURDATE()) AND MONTH(promotion_end) >= MONTH(CURDATE()) AND promotion_flag = 1
                   ORDER BY promotion_id DESC`,
            []
          )
          console.log(typeof res);
          
          return responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Get Promotion ${e}`, 407);
        }
    }
}

export let GetPromotionModule = new GetPromotionController();