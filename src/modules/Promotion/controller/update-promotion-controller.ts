import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class updatePromotionController {

    public async updatePromotion(req: any) {
        try {
            let bodyData = await req.data;

            var dateformat = new Date(bodyData.promotion_end);
            let value = {
                user_edit_id: bodyData.user_edit_id,
                promotion_name: bodyData.promotion_name,
                promotion_body: bodyData.promotion_body,
                promotion_pic: bodyData.promotion_pic,
                promotion_date: bodyData.promotion_date,
                promotion_end: bodyData.promotion_end,
                promotion_edit_date: bodyData.promotion_edit_date,
                promotion_flag: bodyData.promotion_flag,
                promotion_edit_flag: bodyData.promotion_edit_flag,
            }
            
            var res = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `UPDATE tbl_promotion SET user_edit_id = ?, promotion_name = ?, promotion_body = ?, promotion_pic = ?, promotion_date = ?, promotion_end = ?, promotion_edit_date = ?, promotion_flag = ?, promotion_edit_flag = ? WHERE promotion_id = ?`,
            [value.user_edit_id, value.promotion_name, value.promotion_body, value.promotion_pic, value.promotion_date, value.promotion_end, value.promotion_edit_date, value.promotion_flag, value.promotion_edit_flag, bodyData.promotion_id]
          )
          
          return responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Update Promotion ${e}`, 407);
        }
    }
}

export let updatePromotionModule = new updatePromotionController();