import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class createPromotionController {

    public async createPromotion(req: any) {
        try {
            
            let bodyData = await req.data;
            let value = {
                user_create_id: bodyData.user_create_id,
                promotion_name: bodyData.promotion_name,
                promotion_body: bodyData.promotion_body,
                promotion_pic: bodyData.promotion_pic,
                promotion_date: bodyData.promotion_date,
                promotion_end: bodyData.promotion_end,
                promotion_flag: bodyData.promotion_flag,
            }
            var res = await connectdocker.connect(
            DatabaseConfig.webPKG,
            // `INSERT INTO tbl_promotion SET ?`,
            "INSERT INTO tbl_promotion (user_create_id, promotion_name, promotion_body, promotion_pic, promotion_date, promotion_end, promotion_flag) VALUES (?, ?, ?, ?, ?, ?, ?)",
            [value.user_create_id, value.promotion_name, value.promotion_body, value.promotion_pic, value.promotion_date, value.promotion_end, value.promotion_flag]
          )
          
          return responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Create Promotion ${e}`, 407);
        }
    }
}

export let createPromotionModule = new createPromotionController();