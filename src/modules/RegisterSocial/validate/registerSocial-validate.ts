import Joi, { any } from "joi"
import {responseError} from "../../../help/response";

export class ValidateRegisterSocial {
    
    public async ValidateRegisterSocial(req: any){
        try {
            const params = await req.data;
            let schema = Joi.object({
                typeRegis: Joi.any(),
                mobile: Joi.any(),
                id: Joi.any(),
                advisor_id: Joi.any(),
                phone_firebase: Joi.any(),
                firstname: Joi.any(),
                lastname: Joi.any(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            console.log("=-=-=-=-=-=-=");
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

}

export let ValidationRegisterSocial = new ValidateRegisterSocial();