import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";

class CheckMemberSocialController {
    
    public async checkMemberSocialWithLine(req:any){
        try{
            const params = await req.params;
            console.log(params);

            var res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT id, mobile, roleId FROM customercenter WHERE userID_line = ?`,
                [params.lineID]
              )
              console.log(res.length);
              if (res.length != 0) {
                return responseSuccess(res[0], 200);
              } else {
                return responseError(res, 404);
              }
        }catch(e){
            console.log(e);
            return await responseError("ERROR :: checkMemberSocialWithLine", 407);
        }
    }

    public async checkMemberSocialWithApple(req:any){
        try{
            const params = await req.params;
            console.log(params);

            var res = await connectdocker(
                DatabaseConfig.webPKG,
                `SELECT id, mobile, roleId FROM customercenter WHERE userID_apple = ?`,
                [params.appleID]
              )
              console.log(res.length);
              if (res.length != 0) {
                return responseSuccess(res[0], 200);
              } else {
                return responseError(res, 404);
              }
        }catch(e){
            console.log(e);
            return await responseError("ERROR :: checkMemberSocialWithLine", 407);
        }
    }
}

export let CheckMemberSocialModule = new CheckMemberSocialController();