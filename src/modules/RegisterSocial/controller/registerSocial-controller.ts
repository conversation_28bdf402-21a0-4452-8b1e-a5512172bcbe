import { connectdocker } from "../../../help/connectDataBase";
import { responseError, responseSuccess } from "../../../help/response";
import { DatabaseConfig } from "../../../help/database.enum";

class RegisterSocialController {

  public async registerSocial(req: any) {
    try {

      let bodyData = await req.data;

      //TODO :: Check User
      const resCheckuser = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT username
         FROM customercenter
         WHERE mobile = ?`,
        [bodyData.mobile]
      );
      if (resCheckuser.length != 0) {
        return await responseError("Phone number is using", 202);
      }
      //TODO :: Check User

      //TODO :: Register
      let display = `${bodyData.firstName} ${bodyData.lastName}`;
      let value = {};
      if (bodyData.typeConnect == "line") {
        value = {
          firstname: bodyData.firstName,
          lastname: bodyData.lastName,
          mobile: bodyData.phone,
          phone_firebase: bodyData.phoneFirebase,
          displayName: bodyData.display,
          fullname: display,
          userID_line: bodyData.userID,
          link_accounts: "Y",
          line_connect: "Y",
          create_user: "Mapp",
          advisor_id: bodyData.refcode ?? ""
        };
      } else if (bodyData.typeConnect == "apple") {
        value = {
          firstname: bodyData.firstName,
          lastname: bodyData.lastName,
          mobile: bodyData.phone,
          phone_firebase: bodyData.phoneFirebase,
          displayName: bodyData.display,
          fullname: display,
          userID_apple: bodyData.userID,
          link_accounts: "Y",
          apple_connect: "Y",
          create_user: "Mapp",
          advisor_id: bodyData.refcode ?? ""
        };
      }


      var columns = Object.keys(value).join(",");
      var placeholders = Object.keys(value).map(() => "?").join(",");
      var values = Object.values(value);
      var sqlQuery = `INSERT INTO customercenter (${columns})
                      VALUES (${placeholders})`;
      var dataInsert = await connectdocker.connect(DatabaseConfig.webPKG, sqlQuery, values);
      //TODO :: Register
      if (dataInsert.affectedRows === 0) {
        return await responseError("ERROR :: Register Social", 407);
      } else {
        var bodyPoi = {
          phone: bodyData.phoneFirebase,
          activityID: KEY_POI_DOWNLOAD,
          firstName: bodyData.firstName,
          lastName: bodyData.lastName,
          merchantID: MERCHANT_ID
        };
        const newRequest = new Request(`${API_URL_LIKEPOINT}/transactions-activity/pay-poi-in-app`, {
          body: JSON.stringify(bodyPoi),
          headers: {
            "Content-Type": "application/json",
            "x-api-key": API_KEY_LIKEPOINT
          },
          method: "POST"
        });
        const res = await fetch(newRequest);
        const resSaveActivity = await res.json();

        if (resSaveActivity.data[1].statusPay == "Completed") {
          return await responseSuccess({
            memberID: dataInsert.lastInsertId,
            resSaveActivity: resSaveActivity.data[1]
          }, 200);
        }
      }

    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: Register Social", 407);
    }
  }
}

export let RegisterSocialModule = new RegisterSocialController();