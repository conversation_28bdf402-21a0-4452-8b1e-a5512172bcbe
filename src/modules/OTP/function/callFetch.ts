import { fetchRequestService } from "../../../help/fetchButTS";
import {responseError, responseSuccess} from "../../../help/response";
import ags_restauth from '@agilesoft/type_ags_authrest2';

const FuncFetch = async (payload: {"phone": any}) => {
    try {
        console.log('sendOTP');
        var auth = new ags_restauth();
        var user = "sms-service";
        var token = "vhy.mxk3kfp@MPN_fuq";

        // auth.R_USER = user;
        // auth.R_TOKEN = token;
        // let bodyData = await req.data;
        
        let sendData = {
            "phone": payload.phone,
            "project_key" : "6915d97c47",
            "User" : "PKG"
        };

        let genTokenEncryp = await auth.genTokenEncryp(token,user);
        
        var enBody = await auth.encrypbody(sendData,token);

        try {
            var ResponseData = await fetchRequestService(
                `https://agilesoftgroup.com/sms-service/MKT_otpsend`,
                enBody,
                genTokenEncryp
              );

              if (ResponseData["code"] == "000") {
                return {
                    "result": ResponseData,
                    "statusCode": 200
                    };
              } else {
                return {
                    "result": ResponseData["detail"],
                    "statusCode": 401
                    };
              }
              
        }catch (e) {
            console.log(e);
            return {
                "result": `ERROR :: Send OTP => ${e}`,
                "statusCode": 407
            };
        }
          
        // return responseSuccess(ResponseData, 200);
        
    } catch (e){
        console.log(e);
        return await responseError("ERROR :: Send OTP", 407);
    }
  };
  
export { FuncFetch }