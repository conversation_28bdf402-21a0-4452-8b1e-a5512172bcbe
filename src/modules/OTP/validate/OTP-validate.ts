import Jo<PERSON> from "joi";
import {Request} from "itty-router";
import {responseError} from "../../../help/response";

export class VaridateOTP {
    
    public async sendOTP(req: any){
        try {
            const paramsSend = await req.data;
        
            console.log(paramsSend);

            let schemaSend = Joi.object({
                phone: Joi.string().required(),
            })

            const {error} = await schemaSend.validate(paramsSend);
            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
            
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async verifyOTP(req: any){
        try {
            const paramsSend = await req.data;
        
            console.log(paramsSend);

            let schemaSend = Joi.object({
                token: Joi.string().required(),
                otp_code: Joi.string().required(),
            })

            const {error} = await schemaSend.validate(paramsSend);
            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
            
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationOTP = new VaridateOTP();