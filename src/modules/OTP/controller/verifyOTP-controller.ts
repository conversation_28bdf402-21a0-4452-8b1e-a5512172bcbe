import { fetchRequestService } from "help/fetchButTS";
import {responseError, responseSuccess} from "../../../help/response";
import { ValidationOTP } from "../validate/OTP-validate";
import ags_restauth from '@agilesoft/type_ags_authrest2';
import { cache } from "joi";

class VerifyOTPController {

    public async verifyOTP(req: any) {
        try {
            console.log('Verify OTP');
            var auth = new ags_restauth();
            var user = "sms-service";
            var token = "vhy.mxk3kfp@MPN_fuq";

            auth.R_USER = user;
            auth.R_TOKEN = token;

            let bodyData = await req.data;
            let verifyData = {
                "token": bodyData.token,
                "otp_code" : bodyData.otp_code,
                "User" : "PKG"
            };

            let genTokenEncryp = await auth.genTokenEncryp(token,user);
            var enBody = await auth.encrypbody(verifyData,token);
                var ResponseData = await fetchRequestService.post(
                    `https://agilesoftgroup.com/sms-service/MKT_otpverify`,
                    enBody,
                    {
                      "Content-Type": "application/json",
                      "Authorization": genTokenEncryp,
                    }
                  );

                  console.log("return");
                  if (ResponseData.code == "000") {
                    return responseSuccess(ResponseData, 200);
                  } else {
                    return responseError(ResponseData.detail, 404);
                  }
                    
        } catch (e){
            console.log(e);
            return await responseError("ERROR :: Verify OTP", 407);
        }
    }
}

export let VerifyOTPModule = new VerifyOTPController();