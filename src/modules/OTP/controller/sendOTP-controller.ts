import { fetchRequestService } from "../../../help/fetchButTS";
import {responseError, responseSuccess} from "../../../help/response";
import { ValidationOTP } from "../validate/OTP-validate";
import { cache } from "joi";
import { FuncFetch } from "../function/callFetch";
import ags_restauth from '@agilesoft/type_ags_authrest2';

class SendOTPController {

    public async sendOTP(req: any) {
        try {
            console.log('router sendOTP');
            var auth = new ags_restauth();
            var user = "sms-service";
            var token = "vhy.mxk3kfp@MPN_fuq";

            // // auth.R_USER = user;
            // // auth.R_TOKEN = token;
            let bodyData = await req.data;
            
            let sendData = {
                "phone": bodyData.phone,
                "project_key" : "6915d97c47",
                "User" : "PKG"
            };

            let genTokenEncryp = await auth.genTokenEncryp(token,user);
            
            var enBody = await auth.encrypbody(sendData,token);
            try {

               let ResponseData = await fetchRequestService.post(`https://agilesoftgroup.com/sms-service/MKT_otpsend`, enBody, genTokenEncryp)
            //    let ResponseData = await fetchRequestService.post(`http://172.16.0.249:64094/sms-service/MKT_otpsend`, enBody, genTokenEncryp)

                return responseSuccess(ResponseData, 200);
            }catch (e) {
                console.log(e);
                return await responseError(`ERROR :: Send OTP => ${e}`, 407);
            }
        } catch (e){
            console.log(e);
            return await responseError("ERROR :: Send OTP", 407);
        }
    }
}

export let SendOTPModule = new SendOTPController();