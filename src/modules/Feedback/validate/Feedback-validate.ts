import { responseError } from "help/response";
import <PERSON><PERSON> from "joi";

export class ValidateFeedBack {
  public async ValidateFeedBack(req: any) {
    try {
      const params = await req.data;

      let schema = Joi.object({
        mobile: Joi.string(),
        name: Joi.string(),
        os: Joi.string(),
        version: Joi.any(),
        star: Joi.any(),
        impressive: Joi.any(),
        comment: Joi.any(),
        merchantID: Joi.any()
      });

      const { error } = await schema.validate(params);

      console.log(error);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }
}

export let ValidationFeedBack = new ValidateFeedBack();
