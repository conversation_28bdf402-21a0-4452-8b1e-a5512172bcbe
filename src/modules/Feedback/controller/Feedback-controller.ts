import { TGNotify } from "help/TG-notify";
import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class FeedbackController {
  public async createFeedback(req: any) {
    try {
      let bodyData = await req.data;
      console.log(bodyData);

      var dataInsert = await connectdocker.connect(
        DatabaseConfig.webPKG,
        "INSERT INTO feedback SET feedback_user_phone = ?, sender_name = ?, feedback_os = ?, feedback_appversion = ?, feedback_star = ?, feedback_impressive = ?, feedback_comment = ?",
        [
          bodyData.mobile,
          bodyData.name ?? "",
          bodyData.os,
          bodyData.version,
          bodyData.star,
          bodyData.impressive,
          bodyData.comment,
        ]
      );

      if (dataInsert.affectedRows === 0) {
        return await responseError("ERROR :: Create Feedback", 407);
      }

      let message = `🔔 แจ้งเตือน ข้อเสนอแนะการพัฒนา APP Prachakij\n`;
      message += `☺️ คุณ : ${bodyData.name}\n`;
      message += `📱 เบอร์โทรศัพท์ : ${bodyData.mobile}\n`;
      message += `⭐️ คะแนน : ${bodyData.star}\n`;
      message += `📝 รายละเอียดข้อเสนอแนะ : ${bodyData.impressive ?? ""} ${
        bodyData.comment ?? ""
      }\n`;
      var tokenTGPrachakij = "-1002085886913";
      var obseverFeedback = "-1001893927719";

      await TGNotify(message, tokenTGPrachakij); ///ส่ง line
      await TGNotify(message, obseverFeedback);

      const nameParts = bodyData.name.split(' ');

      const newRequest = new Request(`${API_URL_LIKEPOINT}/transactions-activity/pay-poi-in-app`, {
        body: JSON.stringify({
          "phone": "+66"+bodyData.mobile.substring(1),
          "activityID": KEY_POI_FEEDBACK,
          "firstName": nameParts[0],
          "lastName": nameParts[1],
          "merchantID": MERCHANT_ID
        }),
        headers: {
        "Content-Type": "application/json",
        "x-api-key": API_KEY_LIKEPOINT
        },
        method: "POST",
      });
      const res = await fetch(newRequest);
      const resPayPOI = await res.json();
      if (resPayPOI.data[1].statusPay == "Completed") {
        return await responseSuccess({
          feedbackID: dataInsert.lastInsertId,
          resSaveActivity: resPayPOI.data[1]
        }, 200);
      }


    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }
}

export let FeedbackModule = new FeedbackController();
