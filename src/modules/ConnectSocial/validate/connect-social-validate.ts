import { responseError } from "help/response";
import <PERSON><PERSON> from "joi";

export class ConnectSocialValidate {
    public async ValidateConnectSocial(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                    phone: Joi.string(),
                    field_id_connect: Joi.string(),
                    field: Joi.string(),
                    id_connect: Joi.any(),
                    status: Joi.string()
            });

            const {error} = await schema.validate(params);

            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationConnectSocial = new ConnectSocialValidate();