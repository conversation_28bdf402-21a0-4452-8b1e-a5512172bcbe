import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class ConnectSocialController {
    public async connectSocial(req: any) {
        try {
            const bodyData = await req.data;

            let value = [
                bodyData.field,
                bodyData.status,
                bodyData.field_id_connect,
                bodyData.id_connect,
                bodyData.phone
            ];

            console.log(value);
            

            let resConnectSocial = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `UPDATE customercenter
                    SET ?? = ?,
                        ?? = ?
                    WHERE mobile = ? LIMIT 1`,
                [bodyData.field, bodyData.status, bodyData.field_id_connect, bodyData.id_connect, bodyData.phone]
            )

            if (resConnectSocial.affectedRows === 0) {
                return await responseError("Connect Social ERROR :: NOT UPDATE", 406);
            }

            return await responseSuccess(resConnectSocial, 200);
        } catch (e) {
            return await responseError(`Catch Connect Social => ${e}`, 407);
        }
    }
}

export let ConnectSocialModule = new ConnectSocialController();