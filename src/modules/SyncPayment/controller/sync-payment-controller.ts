import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { fetchNormal } from "../../../help/fetchNormal";
import { exist } from "joi";
import { QRCodePaymentType } from "../type/sync-payment-type";
import { NotificationModule } from "../../Notification/controller/notification-controller";
import { addOrUpdateNotificationData } from "../../../help/sendNotificationFCM";
import { TGNotify } from "../../../help/TG-notify";

class SyncPaymentController {
  // ฟังก์ชัน cancelPaymentByOrderID
  public async cancelPaymentByOrderID(req: any) {
    try {
      const orderID = req.params.order_id;
  
      // Step 1: Fetch `order_code` and `qr_code_expire` for the specified order
      
      const selectOrderQuery = await connectdocker.connect(
          DatabaseConfig.webPKG,
          `SELECT order_code, qr_code_expire
           FROM orders_auto_parts
           WHERE order_id = ?`,
          [orderID]
      );
  
      // Check if the order exists
      if (!selectOrderQuery || selectOrderQuery.length === 0) {
          return await responseError("Order not found", 404);
      }
  
      // Step 2: Fetch items data for the specified orderID from `orders_auto_parts_items`
      const itemsData = await connectdocker.connect(
          DatabaseConfig.webPKG,
          `SELECT * FROM orders_auto_parts_items
           WHERE order_id = ?`,
          [orderID]
      );
  
      console.log(itemsData);
      console.log("go here");

      // Check if there are items to transfer
      if (itemsData && itemsData.length > 0) {
        // Step 3: Insert items data into `orders_auto_parts_items_del` one by one
        for (const item of itemsData) {
            const insertResult = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `INSERT INTO orders_auto_parts_items_del (
                    item_id, order_id, product_id, product_code, quantity, price, report_p, ship_cost
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    item.item_id,
                    item.order_id, 
                    item.product_id,
                    item.product_code,
                    item.quantity,
                    item.price,
                    "ยกเลิกจากแอพ",
                    item.ship_cost
                ]
            );

            // Verify if the insert operation was successful
            if (!insertResult || insertResult.affectedRows === 0) {
                return await responseError("Failed to transfer item to orders_auto_parts_items_del", 500);
            }
        }

          // Step 4: Delete items data from `orders_auto_parts_items`
          await connectdocker.connect(
              DatabaseConfig.webPKG,
              `DELETE FROM orders_auto_parts_items
              WHERE order_id = ?`,
              [orderID]
          );
      } 

      // Step 6: Update the order to set `qr_code_expire` to NULL and `status_order` to "CANCELLED"
      await connectdocker.connect(
          DatabaseConfig.webPKG,
          `UPDATE orders_auto_parts
          SET qr_code_expire = NULL,
              status_order   = ?
          WHERE order_id = ?`,
          ["CANCELLED", orderID]
      );

      // Step 7: Fetch and return the updated order details
      const response = await connectdocker.connect(
          DatabaseConfig.webPKG,
          `SELECT *
          FROM orders_auto_parts
          WHERE order_id = ?`,
          [orderID]
      );

      return await responseSuccess(response, 200);
    } catch (e) {
        console.error(`ERROR :: Cancel Payment By OrderID => ${e}`);
        return await responseError(
            `ERROR :: Cancel Payment By OrderID => ${e}`, 
            407
        );
    }
  
  }

  // ฟังก์ชัน checkStatusPaymentByOrderID
  public async checkStatusPaymentByOrderID(req: any) {
    try {
      const orderID = req.params.order_id;

      // ดึงข้อมูล order_code และ status_order ในครั้งเดียว
      const selectQuery = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT order_code, status_order, qr_code_expire
         FROM orders_auto_parts
         WHERE order_id = ?`,
        [orderID]
      );

      if (selectQuery.length === 0) {
        return await responseError("Order not found", 404);
      }

      const { status_order, qr_code_expire } = selectQuery[0];

      // ถ้าเป็น "PAYMENT_COMPLETED" และ qr_code_expire ไม่ใช่ null ให้ update qr_code_expire
      if (status_order === "PAYMENT_COMPLETED" && qr_code_expire !== null) {
        await connectdocker.connect(
          DatabaseConfig.webPKG,
          `UPDATE orders_auto_parts
           SET qr_code_expire = NULL
           WHERE order_id = ?
             AND qr_code_expire IS NOT NULL`,
          [orderID]
        );
        return await responseSuccess("Payment completed", 200);
      }

      // ถ้าเป็น "AWAITING_PAYMENT"
      if (status_order === "AWAITING_PAYMENT") {
        const resPayment = await fetchNormal(
          `https://new.likepoint.io/sync-payment-pms`,
          {
            orderID: orderID
          }
        );

        // ตรวจสอบการชำระเงิน
        if (resPayment.statusCode === 200 && resPayment.data[0]) {
          if (resPayment.data[0].paid === "PAID") {
            // อัพเดตสถานะการสั่งซื้อเป็น PAYMENT_COMPLETED
            const infoUpdate = await connectdocker.connect(
              DatabaseConfig.webPKG,
              `UPDATE orders_auto_parts
               SET status_order   = ?,
                   qr_code_expire = ?
               WHERE order_id = ?`,
              ["PAYMENT_COMPLETED", null, orderID]
            );

            // ตรวจสอบว่าการอัพเดตสำเร็จ
            if (infoUpdate.affectedRows === 1) {
              // ดึงข้อมูล order ทั้งหมดเพื่อตอบกลับ
              const response = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT *
                 FROM orders_auto_parts
                 WHERE order_id = ?`,
                [orderID]
              );
              return await responseSuccess(response, 200);
            } else {
              return await responseError("Error in updating status_order", 407);
            }
          } else {
            return await responseSuccess("Awaiting payment", 200);
          }
        } else {
          return await responseError("Error in checking payment status", 407);
        }
      }

      // กรณีสถานะการชำระเงินไม่ตรงกับที่คาดหวัง
      return await responseError("Unexpected payment status", 400);
    } catch (e) {
      console.error(`ERROR :: Check Status Payment By OrderID => ${e}`);
      return await responseError(
        `ERROR :: Check Status Payment By OrderID => ${e}`,
        407
      );
    }
  }

  // ฟังก์ชัน checkStatusPayment ที่มีอยู่แล้ว
  public async checkStatusPayment(req: any) {
    try {
      let response = [];
      let status = "AWAITING_PAYMENT";
      let resGetOrderWaitingPayment = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT order_code
         FROM orders_auto_parts
         WHERE status_order = ?`,
        [status]
      );
      for (let i = 0; i < resGetOrderWaitingPayment.length; i++) {
        let order_code = resGetOrderWaitingPayment[i].order_code;
        var resPayment = await fetchNormal(
          `https://new.likepoint.io/sync-payment-pms`,
          {
            orderID: "สำนักงานใหญ่"
            // orderID: "order_code"
          }
        );
        if (resPayment.statusCode == 200 && resPayment.data[0]) {
          if (resPayment.data[0].paid == "PAID") {
            var infoUpdate = await connectdocker.connect(
              DatabaseConfig.webPKG,
              `UPDATE orders_auto_parts
               SET status_order = ?
               WHERE order_code = ?`,
              ["PAYMENT_COMPLETED", order_code]
            );
            response.push(infoUpdate);
          } else {
            continue;
          }
        } else {
          continue;
        }
      }
      if (response.length > 0) {
        return await responseSuccess(response, 200);
      } else {
        return await responseSuccess("no data", 404);
      }
    } catch (e) {
      return await responseError(`ERROR :: Check Status Payment => ${e}`, 407);
    }
  }

  async qrCodePayment(item: any) {
    try {
      console.log("Request Body:", item); // แสดงข้อมูล body ที่ได้รับ

      exist();
      // อ่านข้อมูลจาก ReadableStream
      const chunks: Uint8Array[] = [];
      for await (const chunk of req.body) {
        chunks.push(chunk);
      }

      // แปลงข้อมูลเป็น string
      const bodyString = Buffer.concat(chunks).toString("utf-8");
      console.log("Request Body:", bodyString); // แสดงข้อมูล body ที่ได้รับ

      // แปลงเป็น JSON
      const { orderId, memberID, phoneMember, amount } = JSON.parse(bodyString);

      // ตรวจสอบว่าข้อมูลที่จำเป็นถูกส่งมาครบถ้วน
      if (!orderId || !memberID || !phoneMember || amount === undefined) {
        throw new Error("Missing required fields in request body");
      }

      // ตรวจสอบประเภทของ amount
      if (typeof amount !== "number") {
        throw new Error("Amount must be a number");
      }

      const now = new Date();
      const requestDt = now.toISOString().split("T")[0];

      // สร้าง body ของ request สำหรับการชำระเงิน QR Code
      const bodyQRCode = {
        txnAmount: amount.toString(),
        reference1: orderId,
        reference2: memberID,
        reference3: null,
        reference4: null,
        metadata: "PMS_SHOP_ONLINE",
        create_user: "",
        phone_number: phoneMember, 
        bu: "PMS",
        type_mode: "123",
        requestDt: requestDt
      };

      console.log("Body QRCode:", bodyQRCode);

      // ส่ง request ไปยัง API ที่สร้าง QR Code
      const resQRCodePayment = await fetchNormal(
        `https://kbank.likepoint.io/requestPayment`,
        bodyQRCode
      );

      console.log("Response QRCode Payment:", resQRCodePayment);
      return resQRCodePayment || {};
    } catch (e) {
      console.error("Error in qrCodePayment:", e);
      throw e; // โยนข้อผิดพลาดกลับไปเพื่อให้สามารถจัดการในที่อื่นได้
    }
  }

  public async qrCodeCancel(partnerTxn: string) {
    try {
      const resQRCodeCancel = await fetchNormal(
        `https://kbank.likepoint.io/cancelPayment`,
        {
          partnerTxnUid: partnerTxn
        }
      );

      return resQRCodeCancel;
    } catch (e) {
      throw e;
    }
  }

  // ฟังก์ชัน checkDataToQRCode ที่มีอยู่แล้ว
  public async genQrCode(req: any) {
    try {
      let bodyData = await req.data;
      const order_id = bodyData.order_id;
      const point_user = bodyData.point_user;
      const total_amount = bodyData.total_amount;
      const discount_amount = bodyData.discount_amount;
      const pocket_id = bodyData.pocket_id;
      const phone_number = bodyData.phone_number;

      const query = `SELECT *
                     FROM orders_auto_parts
                     WHERE order_id = ? LIMIT 1`;

      const resQuery = await connectdocker.connect(
        DatabaseConfig.webPKG,
        query,
        [order_id]
      );

      // ตรวจสอบว่าพบ order_id หรือไม่
      if (resQuery.length === 0) {
        return await responseError(
          "Order not found / ไม่พบ Order ดังกล่าว",
          404
        );
      }

      // Generate QR Code
      try {
        const requestBodyQRCode = {
          orderId: order_id,
          memberID: resQuery[0].member_id,
          phoneMember: phone_number,
          amount: (Number(total_amount) - Number(discount_amount))
        };
        console.log("Request Body QRCode:", requestBodyQRCode);

        const now = new Date();
        const requestDt = now.toISOString().split("T")[0];

        const bodyQRCode = {
          txnAmount: requestBodyQRCode.amount,
          reference1: "สำนักงานใหญ่",
          reference2: requestBodyQRCode.memberID.toString(),
          reference3: "",
          reference4: "",
          metadata: "PMS_SHOP_ONLINE",
          create_user: "",
          phone_number: requestBodyQRCode.phoneMember,
          bu: "PMS",
          type_mode: "123",
          requestDt: requestDt
        };

        console.log("Body QRCode:", bodyQRCode);

        // ส่ง request ไปยัง API ที่สร้าง QR Code
        const resQRCodePayment = (await fetchNormal(
          `https://kbank.likepoint.io/requestPayment`,
          bodyQRCode
        )) as QRCodePaymentType;

        if (resQRCodePayment.qrCode && resQRCodePayment.partnerTxnUid) {
          now.setMinutes(now.getMinutes() + 10);

          // ตั้งค่าการหมดอายุของ QR Code
          var timestampExp = now.getTime();
          console.log("Response QRCode Payment:", resQRCodePayment);
          console.log("Timestamp Expire:", timestampExp);

          // Pay Points
          const merchantWalletID = "effdfc69-1b3f-41f7-a6c2-99d3ffd4ad19";
          const merchantPocketID = "5355f7f0-dfb8-44ef-a198-94463e823907";

          // Fetch pocket information

          const newRequestInfoPocket = new Request(
            `${API_URL_LIKEPOINT}/pocket/get-pocket/${pocket_id}`,
            {
              headers: {
                "Content-Type": "application/json",
                "x-api-key": API_KEY_LIKEPOINT
              },
              method: "GET"
            }
          );

          const resInfoPocket = await fetch(newRequestInfoPocket);

          // ตรวจสอบว่าได้ pocket ข้อมูลจาก API หรือไม่
          if (resInfoPocket.status == 200) {
            const pocketJson = await resInfoPocket.json();
            const pocketMember = pocketJson.data as PocketType;

            // Body สำหรับการโอนเงิน
            const bodyUsePoint: TransactionsType = {
              toPocketID: merchantPocketID,
              toWalletID: merchantWalletID,
              fromPocketID: pocketMember.id,
              fromWalletID: pocketMember.walletID,
              transactionExplain: `ใช้ PMSpoint เป็นส่วนลด #${order_id}`,
              amount: point_user as unknown as Float32Array
            };
            console.log("Body Use Point:", bodyUsePoint);

            // ทำการโอนเงิน
            const newRequestTransfer = new Request(
              `${API_URL_LIKEPOINT}/transactions`,
              {
                body: JSON.stringify(bodyUsePoint),
                headers: {
                  "Content-Type": "application/json",
                  "x-api-key": API_KEY_LIKEPOINT
                },
                method: "POST"
              }
            );


            var updateQuery = '';
            var updateParams = [];
            if (point_user != 0) {
              const resTransfer = await fetch(newRequestTransfer);
              // ตรวจสอบว่าโอนเงินสำเร็จหรือไม่
              if (resTransfer.status == 200) {
                const transferJson = await resTransfer.json();
                const tx_point = transferJson.data.id;

                // Update order with QR Code and transaction details
                updateQuery = `
                    UPDATE orders_auto_parts
                    SET qr_data         = ?,
                        qr_code_expire  = ?,
                        tx_point        = ?,
                        partner_txn     = ?,
                        discount_points = ?,
                        total_amount    = ?
                    WHERE order_id = ?`;

                updateParams = [
                  resQRCodePayment.qrCode,
                  timestampExp,
                  tx_point,
                  resQRCodePayment.partnerTxnUid,
                  point_user,
                  (total_amount - discount_amount),
                  order_id
                ];

                //TODO :: Update order with QR Code
                const infoUpdate = await connectdocker.connect(
                  DatabaseConfig.webPKG,
                  updateQuery,
                  updateParams
                );

                if (infoUpdate.affectedRows === 0) {
                  return await responseError(
                    "Cannot update QRCode / ไม่สามารถอัปเดต QRCode ได้",
                    404
                  );
                }

                // Fetch updated order details
                const resultQuery = `
                    SELECT *
                    FROM orders_auto_parts
                    WHERE order_id = ?`;
                const resultParams = [order_id];
                const result = await connectdocker.connect(
                  DatabaseConfig.webPKG,
                  resultQuery,
                  resultParams
                );
               
                if (result.length === 0) {
                  console.log("No order found with the given order_id.");
                  return await responseError(
                    "No order found with the given order_id. / ไม่พบ Order ด้วย ID ดังกล่าว",
                    404
                  );
                }

                // Return success response
                return await responseSuccess(result[0], 200);
                //TODO :: Update order with QR Code
              } else {
                console.error(
                  "Error in transferring points / โอนแต้มไม่สำเร็จ",
                  resTransfer.status
                );
                return await responseError(
                  "Error in transferring points / โอนแต้มไม่สำเร็จ",
                  resTransfer.status
                );
              }
            } else {
              // Update order with QR Code
              updateQuery = `
                  UPDATE orders_auto_parts
                  SET qr_data        = ?,
                      qr_code_expire = ?,
                      tx_point       = ?,
                      partner_txn    = ?,
                      total_amount   = ?
                  WHERE order_id = ?`;

              updateParams = [
                resQRCodePayment.qrCode,
                timestampExp,
                resQRCodePayment.partnerTxnUid,
                point_user,
                total_amount,
                order_id
              ];

              const infoUpdate = await connectdocker.connect(
                DatabaseConfig.webPKG,
                updateQuery,
                updateParams
              );
              if (infoUpdate.affectedRows === 0) {
                return await responseError(
                  "Cannot update QRCode / ไม่สามารถอัปเดต QRCode ได้",
                  404
                );
              }

              // Fetch updated order details
              const resultQuery = `
                  SELECT *
                  FROM orders_auto_parts
                  WHERE order_id = ?`;
              const resultParams = [order_id];
              const result = await connectdocker.connect(
                DatabaseConfig.webPKG,
                resultQuery,
                resultParams
              );
            }

            //TODO :: Update order with QR Code
            await connectdocker.connect(
              DatabaseConfig.webPKG,
              updateQuery,
              updateParams
            );

            // Fetch updated order details
            const resultQuery = `
                    SELECT *
                    FROM orders_auto_parts
                    WHERE order_id = ?`;
            const resultParams = [order_id];
            const result = await connectdocker.connect(
              DatabaseConfig.webPKG,
              resultQuery,
              resultParams
            );

            if (result.length === 0) {
              console.log("No order found with the given order_id.");
              return await responseError(
                "No order found with the given order_id. / ไม่พบ Order ด้วย ID ดังกล่าว",
                404
              );
            }

            // Return success response
            return await responseSuccess(result[0], 200);
            //TODO :: Update order with QR Code

          } else {
            console.error(
              "Error fetching pocket information / ไม่สามารถดึงข้อมูล pocket ได้",
              resInfoPocket.status
            );
            return await responseError(
              "Error fetching pocket information / ไม่สามารถดึงข้อมูล pocket ได้",
              resInfoPocket.status
            );
          }
        } else {
          console.error("Error generating QR Code / สร้าง QR Code ไม่สำเร็จ");
          return await responseError(
            "Error generating QR Code / สร้าง QR Code ไม่สำเร็จ",
            500
          );
        }
      } catch (e) {
        console.error(
          "Error in QR Code generation flow / เกิดข้อผิดพลาดในการสร้าง QR Code",
          e
        );
        return await responseError(
          `Error in QR Code generation flow / เกิดข้อผิดพลาดในการสร้าง QR Code: ${e}`,
          500
        );
      }
    } catch (e) {
      console.error(
        "General error in checkDataToQRCode / ข้อผิดพลาดทั่วไปใน checkDataToQRCode",
        e
      );
      return await responseError(`ERROR :: Check Data To QRCode => ${e}`, 407);
    }
  }

  public async payPoints(pocketID: string, amount: number, orderID: string) {
    try {
      const merchantWalletID = "effdfc69-1b3f-41f7-a6c2-99d3ffd4ad19";
      const merchantPocketID = "5355f7f0-dfb8-44ef-a198-94463e823907";

      // Fetch pocket information
      const resInfoPocket = await fetch(
        `${API_URL_LIKEPOINT}/pocket/get-pocket/${pocketID}`,
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": API_KEY_LIKEPOINT
          },
          method: "GET"
        }
      );

      if (!resInfoPocket.ok) {
        // ถ้าการตอบกลับไม่ใช่ 200-299 ให้ throw error ทันที
        throw new Error(`Error fetching pocket info: ${resInfoPocket.status}`);
      }

      const pocketJson = await resInfoPocket.json();
      const pocketMember = pocketJson.data as PocketType;

      // Body สำหรับการโอนเงิน
      const bodyUsePoint: TransactionsType = {
        toPocketID: merchantPocketID,
        toWalletID: merchantWalletID,
        fromPocketID: pocketMember.id,
        fromWalletID: pocketMember.walletID,
        transactionExplain: `${orderID} - จ่ายเงินสินค้า`,
        amount: amount
      };

      // ทำการโอนเงิน
      const resTransfer = await fetch(`${API_URL_LIKEPOINT}/transfer`, {
        body: JSON.stringify(bodyUsePoint),
        headers: {
          "Content-Type": "application/json",
          "x-api-key": API_KEY_LIKEPOINT
        },
        method: "POST"
      });

      if (!resTransfer.ok) {
        // ถ้าการตอบกลับไม่ใช่ 200-299 ให้ throw error
        throw new Error(`Error during transfer: ${resTransfer.status}`);
      }

      const transferInfo = await resTransfer.json();
      return transferInfo.data.id;
    } catch (error) {
      console.error("An error occurred during the payPoints process:", error);
      throw error;
    }
  }

  public async updatePaymentSlip(req: any) {
    try {
      // Destructuring request data
      // const { order_id, url_slip, total_amount, point_user, discount_amount, pocket_id } = await req.data;
      const bodyData = await req.data;

      // Constants for merchant's wallet and pocket IDs
      const merchantWalletID = "effdfc69-1b3f-41f7-a6c2-99d3ffd4ad19";
      const merchantPocketID = "5355f7f0-dfb8-44ef-a198-94463e823907";

      // Fetch pocket information
      const pocketResponse = await fetch(`${API_URL_LIKEPOINT}/pocket/get-pocket/${bodyData.pocket_id}`, {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": API_KEY_LIKEPOINT
        },
        method: "GET"
      });

      if (!pocketResponse.ok) {
        console.error("Error fetching pocket information", pocketResponse.status);
        return responseError("Error fetching pocket information", pocketResponse.status);
      }

      const pocketData = await pocketResponse.json();
      const pocketMember = pocketData.data as PocketType;

      // Preparing transaction details for point transfer
      const transactionDetails: TransactionsType = {
        toPocketID: merchantPocketID,
        toWalletID: merchantWalletID,
        fromPocketID: pocketMember.id,
        fromWalletID: pocketMember.walletID,
        transactionExplain: `Using PMS points as discount for #${bodyData.order_id}`,
        amount: bodyData.point_user as unknown as Float32Array
      };


      let updateQuery = ""
      let updateParams = [];
      if (bodyData.point_user != 0) {
        // Perform point transfer
        const transferResponse = await fetch(`${API_URL_LIKEPOINT}/transactions`, {
          body: JSON.stringify(transactionDetails),
          headers: {
            "Content-Type": "application/json",
            "x-api-key": API_KEY_LIKEPOINT
          },
          method: "POST"
        });

        if (!transferResponse.ok) {
          console.error("Error in transferring points", transferResponse.status);
          return responseError("Error in transferring points", transferResponse.status);
        }

        // Fetch transaction details
        const transferData = await transferResponse.json();
        const tx_point = transferData.data.id;
        // Update order details in the database
        updateQuery = `UPDATE orders_auto_parts
                       SET slip_payment    = ?,
                           total_amount    = ?,
                           discount_points = ?,
                           tx_point        = ?
                       WHERE order_id = ?`;
        updateParams = [bodyData.url_slip, (bodyData.total_amount - bodyData.discount_amount), bodyData.point_user, tx_point, bodyData.order_id];

      } else {
        // Update order details in the database
        updateQuery = `UPDATE orders_auto_parts
           SET slip_payment = ?,
               total_amount = ?
           WHERE order_id = ?`;
        updateParams = [bodyData.url_slip, bodyData.total_amount, bodyData.order_id];
      }

      //TODO :: Update order with QR Code
      await connectdocker.connect(
        DatabaseConfig.webPKG,
        updateQuery,
        updateParams
      );

      // if (infoUpdate.affectedRows === 0) {
      //   return await responseError(
      //     "Cannot update QRCode / ไม่สามารถอัปเดต QRCode ได้",
      //     404
      //   );
      // }

      // Fetch updated order details
      const resultQuery = `
                    SELECT *
                    FROM orders_auto_parts
                    WHERE order_id = ?`;
      const resultParams = [bodyData.order_id];
      const result = await connectdocker.connect(
        DatabaseConfig.webPKG,
        resultQuery,
        resultParams
      );

      if (result.length === 0) {
        console.log("No order found with the given order_id.");
        return await responseError(
          "No order found with the given order_id. / ไม่พบ Order ด้วย ID ดังกล่าว",
          404
        );
      }

      // TODO :: GET MEMBER INFO
      let memberPhone = "";
      let memberName = "";
      const queryMemberInfo = `
        SELECT mobile, fullname
        FROM customercenter 
        WHERE id = ? 
        LIMIT 1
      `;
      const memberInfoResult = await connectdocker.connect(
        DatabaseConfig.webPKG,
        queryMemberInfo,
        [result[0].member_id]
      );
      if (memberInfoResult.length != 0) {
        memberPhone = memberInfoResult[0].mobile;
        memberName = memberInfoResult[0].fullname;

      }
      // TODO :: GET MEMBER INFO

      const message = `บริษัทได้รับสลิปการชำระค่าอะไหล่ประดับยนต์เรียบร้อยแล้ว\nสำหรับหมายเลขคำสั่งซื้อ : ${result[0].order_code}\n\nขอขอบคุณลูกค้าที่ให้ความไว้วางใจและสั่งซื้อสินค้ากับเราครั้งนี้\n\nขอบคุณสำหรับการชำระค่าสินค้ากับทางบริษัทฯ\nบริษัทฯ ยินดีรับใช้และให้บริการ`;
      // const messageTG = `หมายเลขคำสั่งซื้อ : ${result[0].order_code} ของ ${memberName} ลูกค้าอัพหลักฐานการชำระเงินเรียบร้อยแล้ว`;
      const messageTG = `รับชำระเงินเรียบร้อย\n\nคุณ  :: ${memberName}\nหมายเลขคำสั่งซื้อ :: ${result[0].order_code}\nยอดเงินที่ชำระ :: ${(bodyData.total_amount - bodyData.discount_amount)}\n\n ลิงค์สลิป :: ${bodyData.url_slip}\n\nลูกค้าอัพหลักฐานการชำระเงินเรียบร้อยแล้ว\n\n@oraphanaks\n@tharatepsuk\n@mod_191022\n@chakhrithong`;

      const bodyNotification = {
        phone: memberPhone,
        title: "อัพสลิปชำระเงินเรียบร้อย",
        detail: message.substring(0, 20),
      };

      await TGNotify(messageTG, "-4561406239");
      NotificationModule.sendNotificationAppPMSINAPI(bodyNotification);
      const bodyNotiInApp = {
        fields: {
          title: { stringValue: "อัพสลิปชำระเงินเรียบร้อย" }, // String
          detail: { stringValue: message }, // String
          type: { stringValue: "notificationLink" }, // String
          url: { stringValue: "" }, // String
          create: { timestampValue: { seconds: Math.floor(Date.now() / 1000), nanos: 0 } } // Firestore Timestamp
        }
      };

      

      // if (resNotification) {
      //   console.log("Notification ส่งสำเร็จ:", resNotification);
      // } else {
      //   console.error("ส่ง Notification ล้มเหลว");
      // }

      addOrUpdateNotificationData(memberPhone, bodyNotiInApp);


      // Return success response
      return await responseSuccess(result[0], 200);

    } catch (error) {
      console.error("An error occurred while updating payment slip", error);
      return responseError(`ERROR :: Update Payment Slip => ${error}`, 407);
    }
  }

}

export interface PocketType {
  id: string;
  createdAt: Date;
  modifiedAt: null;
  modifiedBy: null;
  documentStatus: boolean;
  walletID: string;
  pocketType: string;
  pocketName: string;
  pocketBalance: number;
  merchantID: string;
  wallet: WalletType;
}

export interface WalletType {
  id: string;
  createdAt: Date;
  modifiedAt: null;
  modifiedBy: null;
  documentStatus: boolean;
  memberID: null;
  merchantID: string;
  walletBalance: number;
}

export interface TransactionsType {
  fromWalletID?: string;
  fromPocketID?: string;
  toWalletID?: string;
  toPocketID?: string;
  transactionExplain?: string;
  amount?: Float32Array;
}

export let SyncPaymentModule = new SyncPaymentController();
