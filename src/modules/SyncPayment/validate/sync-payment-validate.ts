import Joi from "joi";
import { responseError } from "../../../help/response";

export class ValidateSyncPayment {
  public async genQrCode(req: any) {
    try {
      const params = await req.data;
      let schema = Joi.object({
        order_id: Joi.required(),
        point_user: Joi.required(),
        total_amount: Joi.any().required(),
        pocket_id: Joi.required(),
        phone_number: Joi.required(),
        discount_amount: Joi.number().required(),
      });

      const {error} = await schema.validate(params);

      console.log(error);
      console.log("=-=-=-=-=-=-=");
      if(error) {
        return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
      }

    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }

  public async updatePaymentSlip(req: any) {
    try {
      const params = await req.data;
      let schema = Joi.object({
        order_id: Joi.any().required(),
        url_slip: Joi.string().required(),
        total_amount: Joi.number().required(),
        point_user: Joi.number().required(),
        discount_amount: Joi.number().required(),
        pocket_id: Joi.string().required()
      });

      const {error} = await schema.validate(params);

      console.log("=-=-=-=-=-=-=");
      if(error) {
        return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
      }

    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }

}

export let ValidationSyncPayment = new ValidateSyncPayment();