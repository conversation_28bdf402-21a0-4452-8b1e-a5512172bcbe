import {responseError, responseSuccess} from "../../../help/response";
import ags_authrest from '@agilesoft/type_ags_authrest2' 

class EncyptController {

    public async encyptBody(req: any) {
        try {
            req.data = await req.json();
            let params = await req.data;
            let checkHead = await req.headers.get("x-api-key");

            if(checkHead != GET_ENCYPT_KEY){
                return responseError(`อย่ามาวุ่น`, 401);
            }

            var auth = new ags_authrest(R_TOKEN, R_USER)
        
            console.log(params);
            
            var body = await auth.encrypbody(params);
            var header = await auth.genTokenEncryp();

            
            var output = {
                "body": body,
                "header": header,
            }
            
          
          return responseSuccess(output, 200);
        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Get News By ID ${e}`, 407);
        }
    }
}

export let EncyptIDModule = new EncyptController();