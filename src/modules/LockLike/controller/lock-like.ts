import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { fetchData } from "help/fetch";
import { responseError } from "help/response";
import { fetchNormal } from "help/fetchNormal";


class LockLikeController {
    public async payLike(phone: any, idActivity: any, refPhone: any){
      try {
        const body = {
          phone : phone,
          idActivity : idActivity,
          refPhone : refPhone
        };
        var resPayLike = await fetchNormal(
          `https://oxphgjyvu2.execute-api.ap-southeast-1.amazonaws.com/latest/payLikeLock`,
          body,
          {
            "Content-Type": "application/json",
            'x-api-key': 'FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky'
          }
        )

        return resPayLike;

      } catch (e){
        console.log(e);
        return await responseError("ERROR :: payLike", 407);
      }
    }

    public async insertTransactionPOI(phone: any, running_ads: any, type_ads: any, running_poi: any){
        try{
            const formattedPhoneFirebase = `+66${phone.substring(1)}`;    
            var res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `INSERT INTO tb_poi_history 
                       (phone, phone_firebase, running_ads, type_ads, running_poi) 
                       VALUES(?, ?, ?, ?, ?)`,
                    [phone, formattedPhoneFirebase, running_ads, type_ads, running_poi]
            );

            return 200;
        }catch (e){
            console.log(e);
            return 407;
        }
        
    }

    async getBalanceByphoneNumber(phoneCode: string) {  
      try {

        const body = {
          "phoneNumber" : phoneCode
        };

        const response = await fetchData("https://new.likepoint.io/newGetBalanceByphoneNumber",
          body,
        );
    
        return response;
    
      } catch (error) {
        console.error('Error fetching balance', error);
        throw error;
      }
    
    }
      

}

export let LockLikeModule = new LockLikeController();