
import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class UpdatePaymentStatusController {
    public async updatePaymentStatus(req: any) {
        try {
            const paramData = await req.params;
            let sql = `UPDATE tracking_payment SET status = "PAID" WHERE partnerTxnUid = ?`;
            let res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                sql,
                [paramData.partnerTxnUid]
            );

            if (res.affectedRows === 0) {
                return await responseError("UpdatePaymentStatus unsuccess", 404);
            }

            return await responseSuccess("UpdatePaymentStatus success", 200);
            
        } catch (error) {
            return await responseError(error, 407);
        }
    }
}

export let UpdatePaymentStatusModule = new UpdatePaymentStatusController();