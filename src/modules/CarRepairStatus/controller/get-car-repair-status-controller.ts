import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { fetchData } from "help/fetch";
import { responseError, responseSuccess } from "help/response";

class GetCarRepairStatusController {
  public async getCarRepairStatus(req: any) {
    try {
      let bodyData = await req.data;
      let userID = "";
      const carList: any[] = [];

      ///STEP 1 :: GET ID
      const sqlCustomer = `SELECT id FROM customercenter WHERE mobile = ?`;
      let resCustomer = await connectdocker.connect(
        DatabaseConfig.webPKG,
        sqlCustomer,
        [bodyData.phone]
      );

      if (resCustomer.length === 0) {
        return await responseError("GetCarRepairStatus not found", 404);
      }

      userID = resCustomer[0].id;
      ///STEP 2 :: GET CAR Reapir
      const sqlCar = `SELECT running, create_time, name, phone, reg, engin, status, detail_vhc
                            FROM sa_image 
                            WHERE phone IN (?)
                            AND date(create_time) = CURDATE()
                            GROUP BY engin 
                            ORDER BY running DESC
                            LIMIT 1
                            `;

      let resCar = await connectdocker.connect(DatabaseConfig.MIRAI, sqlCar, [
        [bodyData.phone],
      ]);

      if (resCar.length === 0) {
        return await responseError("GetCarRepairStatus not found", 404);
      }

      carList.push({
        userID: userID,
        running: resCar[0].running,
        name: resCar[0].name,
        phone: resCar[0].phone,
        reg: resCar[0].reg,
        machine_number: resCar[0].engin,
        status: resCar[0].status,
        detail_vhc: resCar[0].detail_vhc,
      });

      ///STEP 3 :: GET CAR Contract
      const sqlContract = `
                    SELECT BCT_CONTRACT.colorName, BCT_CONTRACT.colorCode, BCT_CONTRACT.carCode, 
                            MarginSale.car_model, MarginSale.car_model_sa, MarginSale.car_type
                    FROM BCT_CONTRACT 
                    LEFT JOIN MarginSale ON BCT_CONTRACT.carCode = MarginSale.carCode
                    WHERE BCT_CONTRACT.engineCode IN (?)
                    `;

      let resContract = await connectdocker.connect(
        DatabaseConfig.BCT_AMS1,
        sqlContract,
        [resCar[0].engin]
      );

      if (resContract.length) {
        const contract = resContract[0];
        carList[carList.length - 1].colorCode = contract.colorCode;
        carList[carList.length - 1].colorName = contract.colorName;
        carList[carList.length - 1].carCode = contract.carCode;
        carList[carList.length - 1].car_model = contract.car_model;
        carList[carList.length - 1].car_model_sa = contract.car_model_sa;
        carList[carList.length - 1].car_type = contract.car_type;
        if (
          carList[carList.length - 1].carCode !== "" &&
          carList[carList.length - 1].colorCode !== ""
        ) {
          ///STEP 4 :: GET CAR IMAGE
          let resCarPic = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT *
                                FROM CarForPKG
                                LEFT JOIN CarColorForPKG ON CarForPKG.car_id = CarColorForPKG.car_id
                                LEFT JOIN CarImageForPKG ON CarColorForPKG.color_id = CarImageForPKG.color_id
                                WHERE CarForPKG.type_car = ? AND CarColorForPKG.name = ?`,
            [
              carList[carList.length - 1].carCode,
              carList[carList.length - 1].colorCode,
            ]
          );
          carList[carList.length - 1].car_image = resCarPic[0]?.image_url ?? "";
        }
      }

      ///STEP 5 :: GET Time Car Repair
      let resTimeRepair = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT rono, estimateddeliverytime FROM ListofRepairOrder_DotMatrix_th WHERE engineno = ? ORDER BY running DESC LIMIT 1`,
        [resCar[0].engin]
      );
      if (resTimeRepair.length) {
        carList[carList.length - 1].timeRepair =
          resTimeRepair[0].estimateddeliverytime;
        carList[carList.length - 1].rono = resTimeRepair[0].rono;
      }

      console.log(resCar[0].reg);

      try {
        ///STEP 6 :: GET Car Repair Status Mapp_PMS_API
        var responseStatus = await fetchData(
          `https://dw3xxotzc8.execute-api.ap-southeast-1.amazonaws.com/latest/getCarRepairStatus`,
          {
            carReg: resCar[0].reg,
          }
        );

        if (responseStatus.status === 200) {
          carList[carList.length - 1].status = responseStatus.result.repairStatus;
          carList[carList.length - 1].branch = responseStatus.result.branch;
        }
        ///STEP 7 :: GET Lift and Camera  
        if (
          responseStatus.result.running_lift !== 0 ||
          responseStatus.result.running_lift !== null
        ) {
          let resCamera = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT * FROM garageservice WHERE running = ? LIMIT 1`,
            [responseStatus.result.running_lift]
          );

          if (resCamera.length) {
            carList[carList.length - 1].lift = resCamera[0].lift ?? 0;
            carList[carList.length - 1].camera = resCamera[0].CCTV;
          }
        }
      } catch (err) {
        
        console.log(err);
      }


      ///STEP 8 :: GET Payment
      // Get Data from qr_payment
      const qrSQL = `SELECT partnerTxnUid, update_time, txnAmount, url_pic FROM qr_payment 
               WHERE userID = ? AND date(update_time) = CURDATE() 
               ORDER BY running DESC LIMIT 1`;
      const qrResult = await connectdocker.connect(DatabaseConfig.webPKG, qrSQL, [userID]);

      if (qrResult.length > 0) {
        const qrData = qrResult[0];

        // Insert เข้า tracking_payment
        const insertSQL = `INSERT INTO tracking_payment SET ?`;
        const trackingData = {
          partnerTxnUid: qrData.partnerTxnUid,
          userID: userID,
          status: "pending",
          date_payment: qrData.update_time,
          amount: qrData.txnAmount,
        };
        await connectdocker.connect(DatabaseConfig.webPKG, insertSQL, trackingData);

        // ดึงเบอร์โทรศัพท์ลูกค้า
        const phoneSQL = `SELECT mobile FROM customercenter WHERE id = ? LIMIT 1`;
        const phoneResult = await connectdocker.connect(DatabaseConfig.webPKG, phoneSQL, [userID]);

        if (phoneResult.length > 0) {
          const phone = phoneResult[0].mobile;

          // ⏩ เรียก API ส่ง Notification

          var sendNotiResponse = await fetchData(
            `https://dw3xxotzc8.execute-api.ap-southeast-1.amazonaws.com/latest/sendNotificationPayment`,
            {
              phone: phone, // จากการ query ด้านบน
              title: "แจ้งเตือนการชำระเงิน",
              detail: `กรุณาชำระเงินจำนวน ${qrData.txnAmount} บาท ภายใน 10 นาที`,
              note: "QR Payment",
              QRcode: qrData.url_pic,
              amount: qrData.txnAmount,
              status: "pending",
              partnerTxnUid: qrData.partnerTxnUid,
            }
          );
          console.log(sendNotiResponse);
        }
        // ✅ เพิ่มใน carList ตามโครงเดิมของคุณ
        carList[carList.length - 1].type_pay = "QR";
        carList[carList.length - 1].partnerTxnUid = qrData.partnerTxnUid;
        carList[carList.length - 1].status = "pending";
        carList[carList.length - 1].date_payment = qrData.update_time;
        carList[carList.length - 1].amount = qrData.txnAmount;
      }

      // const payment = `SELECT partnerTxnUid,update_time, txnAmount,url_pic FROM qr_payment WHERE userID = ? AND date(update_time) = CURDATE() ORDER BY running DESC LIMIT 1`;
      // const sqlPayment = `SELECT partnerTxnUid, status, date_payment, amount FROM tracking_payment WHERE userID = ? AND date(date_payment) = CURDATE() ORDER BY running DESC LIMIT 1`;
      // let resPayment = await connectdocker.connect(
      //   DatabaseConfig.webPKG,
      //   sqlPayment,
      //   [userID]
      // );
      // if (resPayment.length) {
      //   carList[carList.length - 1].type_pay = "QR";
      //   carList[carList.length - 1].partnerTxnUid = resPayment[0].partnerTxnUid;
      //   carList[carList.length - 1].status = resPayment[0].status;
      //   carList[carList.length - 1].date_payment = resPayment[0].date_payment;
      //   carList[carList.length - 1].amount = resPayment[0].amount;
      // }

      //STEP 9 :: GET Status payment from MIRAI_SUMMARY_SIGN
      const sqlPaymentStatus = `SELECT running, url_file_org, url_file_receipt, pay_total FROM MIRAI_SUMMARY_SIGN WHERE ref_id = ? AND date(create_time) = CURDATE()  ORDER BY running DESC LIMIT 1`;
      let resPaymentStatus = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        sqlPaymentStatus,
        [carList[carList.length - 1].rono]
      );

      if (resPaymentStatus.length) {
        carList[carList.length - 1].type_pay = "cash";
        carList[carList.length - 1].url_file_org =
          resPaymentStatus[0].url_file_org ?? "";
        carList[carList.length - 1].url_file_receipt =
          resPaymentStatus[0].url_file_receipt ?? "";
        carList[carList.length - 1].amount = resPaymentStatus[0].pay_total;
        if (resPaymentStatus[0].url_file_org != "") {
          carList[carList.length - 1].status = "pending";
        } else if (resPaymentStatus[0].pay_total == 0.0) {
          carList[carList.length - 1].status = "PAID";
        }
        if (
          resPaymentStatus[0].url_file_receipt != "" &&
          resPaymentStatus[0].url_file_receipt != "null" &&
          resPaymentStatus[0].url_file_receipt != null
        ) {
          carList[carList.length - 1].status = "PAID";
        }
      }

      return await responseSuccess(carList, 200);
    } catch (error) {
      console.log(error);
      await responseError(error, 407);
    }
  }

  public async getPhoneByCarReg(req: any) {
    try {
      let bodyDate = await req.data;
      let res = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT phone FROM sa_image WHERE reg = ? ORDER BY running DESC LIMIT 1`,
        [bodyDate.carReg]
      );

      console.log(res);
      if (res.length === 0) {
        return await responseError("getPhoneByCarReg not found", 404);
      }

      return await responseSuccess(res[0], 200);
    } catch (error) {
      console.log(error);
      await responseError(error, 407);
    }
  }
}

export let GetCarRepairStatusModule = new GetCarRepairStatusController();
