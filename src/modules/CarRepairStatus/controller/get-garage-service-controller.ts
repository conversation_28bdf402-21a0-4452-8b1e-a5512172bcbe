import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class GarageServiceController {
  public async getGarageService(req: any) {
    try {
      const bodyData = await req.data;
      console.log(bodyData);
      const sql = `SELECT * FROM garageservice WHERE branch = ? AND car_type = ?`;
      let res = await connectdocker.connect(DatabaseConfig.webPKG, sql, [
        bodyData.branch,
        bodyData.car_type,
      ]);

      if (res.length === 0) {
        return await responseError("getGarageService not found", 404);
      }

      return await responseSuccess(res, 200);
    } catch (error) {
      return await responseError("ERROR :: getLifetimeAppointment ", 407);
    }
  }
}

export let GarageServiceModule = new GarageServiceController();
