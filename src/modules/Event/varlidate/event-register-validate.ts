import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateEventRegister {
  public async ValidateEventRegister(req: any) {
    try {
      const params = await req.data;
      let schema = Joi.object({
        id_activity: Joi.string().required(),
        member: Joi.string().required(),
        assessment: Joi.any(),
        reward: Joi.any(),
      });

      const { error } = await schema.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }

  public async ValidateGetEventRegisterById(req: any) {
    try {
      const params = await req.data;
      let schema = Joi.object({
        id_activity: Joi.string().required(),
        member: Joi.string().required(),
      });

      const { error } = await schema.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }
}

export let ValidationEventRegister = new ValidateEventRegister();
