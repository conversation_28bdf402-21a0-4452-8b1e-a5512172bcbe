import { lineNotify } from "help/line-notify";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { NotificationModule } from "../../../modules/Notification/controller/notification-controller";

class EventRegisterController {
  public async eventRegister(req: any) {
    try {
      var bodyData = await req.data;

      let resDataActivity = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT name_activity, fdate_regis_activity,edate_regis_activity,fdate_activity,edate_activity,name_activity 
                    FROM id_activity 
                    WHERE id_activity = ? 
                    LIMIT 1`,
        [bodyData.id_activity]
      );

      if (resDataActivity.length == 0) {
        return await responseError("Event not found", 404);
      }

      // Check date & Convert Date.
      var dataEvent = resDataActivity[0];
      let resMemberActivity = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT * FROM member_activity WHERE activity_id =? AND member_uid = ? `,
        [bodyData.id_activity, bodyData.member]
      );

      if (resMemberActivity.length !== 0) {
        return await responseError("You have already applied", 513);
      }

      let valueInsert = {
        update_user: null,
        create_user: null,
        activity_id: bodyData.id_activity,
        member_uid: bodyData.member,
        assessment: bodyData.assessment,
        reward: bodyData.reward,
      };

      let resInsert = await connectdocker.connect(
        DatabaseConfig.webPKG,
        "INSERT INTO member_activity (update_user, create_user, activity_id, member_uid, assessment, reward) VALUES (?, ?, ?, ?, ?, ?)",
        [
          valueInsert.update_user,
          valueInsert.create_user,
          valueInsert.activity_id,
          valueInsert.member_uid,
          valueInsert.assessment,
          valueInsert.reward,
        ]
      );
      const reqNotificationInApp = {
        data: {
          phone: bodyData.service_date_tel,
          type: "notificationLink",
          title: "ลงทะเบียนกิจกรรม",
          detail: `${dataEvent.name_activity} เรียบร้อยแล้ว`,
          note: "การลงทะเบียนและเงื่อนไขสิทธิการลุ้นรับรางวัลเป็นไปตามที่บริษัทกำหนด",
        },
      };

      let message = `🔔 แจ้งเตือน การลงทะเบียนกิจกรรม\n`;
      message += ` 🎉🎉คุณได้ทำการลงทะเบียนกิจกรรม\n`;
      message += `${dataEvent.name_activity} เรียบร้อยแล้ว\n`;
      message += `กรุณาแจ้งทีมงาน เพื่อรับของที่ระลึก 🎁\n`;
      message += `ขอบคุณค่ะ 🙏🏻`;

      let resMember = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT mobile FROM customercenter WHERE id = ?`,
        [bodyData.member]
      );

      let formattedNumber =
        resMember[0].mobile.slice(0, 3) + "-" + resMember[0].mobile.slice(3);

      let resTokenLine = await connectdocker.connect(
        DatabaseConfig.BCT_AGS,
        `SELECT token FROM BCT_Line_Token_customers WHERE centerPhone LIKE ?`,
        [`%${formattedNumber}%`]
      );

      if (resTokenLine.length != 0) {
        await lineNotify(resTokenLine[0].token, message); ///ส่ง line
      }

      await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp); ///ส่ง app

      return await responseSuccess(
        {
          regisActivty: true,
          resInsert,
        },
        200
      );
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: Create Event Register", 407);
    }
  }

  public async getEventRegisterById(req: any) {
    try {
      var bodyData = await req.data;

      let resCheckRegister = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `SELECT * FROM member_activity WHERE activity_id = ? AND member_uid = ?`,
        [bodyData.id_activity, bodyData.member]
      );

      if (resCheckRegister.length == 0) {
        return await responseError("Event not found", 404);
      }

      return await responseSuccess(resCheckRegister[0], 200);
    } catch (e) {
      console.log(e);
      return await responseError("ERROR :: Get Event Register", 407);
    }
  }
}

export let EventRegisterModule = new EventRegisterController();
