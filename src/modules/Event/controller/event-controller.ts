import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class EventController {
    public async getEvent(req: any) {
        try {
            const res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT 
    id_activity.running, 
    id_activity.id_activity, 
    id_activity.name_activity, 
    id_activity.information_id, 
    id_activity.rate_activity, 
    id_activity.map_activity,
    id_activity.LP_activity, 
    id_activity.QR_activity, 
    id_activity.fdate_regis_activity, 
    id_activity.edate_regis_activity, 
    id_activity.fdate_activity, 
    id_activity.edate_activity,
    tbl_information.information_name, 
    tbl_information.information_body, 
    tbl_information.information_pic
FROM 
    id_activity
LEFT JOIN 
    tbl_information ON id_activity.information_id = tbl_information.information_id
WHERE 
    NOW() BETWEEN id_activity.fdate_regis_activity AND id_activity.edate_activity
ORDER BY 
    id_activity.fdate_activity DESC

                       `,
                []
            )
            if (res.length == 0) {
                return await responseError("Event not found", 404);
            }

            return responseSuccess(res, 200);
        } catch (e) {
            console.log(e);
            return await responseError("ERROR :: Get Event", 407);
        }
    }


}

export let EventModule = new EventController();