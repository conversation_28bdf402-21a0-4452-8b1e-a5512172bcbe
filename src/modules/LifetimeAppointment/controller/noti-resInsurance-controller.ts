import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { NotificationModule } from "../../../modules/Notification/controller/notification-controller";

class NotiInsurance {
    public async sendNotiInsurance() {
        try {
            let res = await connectdocker.connect(
                DatabaseConfig.BCT_AMS1,
                `SELECT MAX(centerName) AS name, 
                centerPhone AS phone, 
                MAX(carRegis) AS car_regg, 
                MAX(create_time) AS last_date,
                MAX(isr_type_date_end_insurance) AS end_date
                FROM BCT_INSURANCE
                WHERE create_time >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
                GROUP BY engineCode`,
                []
            );

            if (res.length === 0) {
                return await responseError("Noti Insurance not found", 404);
            }

            const modifiedData = res.map(item => ({
                ...item,
                phone: item.phone.replace(/[-\s]/g, "")
              }));

              let currentDate = new Date(); // วันเวลาปัจจุบัน
        
              for (let i = 0; i < modifiedData.length; i++) {
                  let date = new Date(modifiedData[i].end_date);

                  let timeDifference = Math.abs(currentDate.getTime() - date.getTime());
                  let daysDifference = Math.floor(timeDifference / (1000 * 3600 * 24));
                  console.log(daysDifference);
                  
                  let message = `🔸 คุณ :: ${modifiedData[i].name}\n`
                      message += `🔸 ทะเบียนรถ :: ${modifiedData[i].car_reg === undefined ? "ไม่ทราบข้อมูล" : modifiedData[i].car_reg}\n`
                      message += `⏰ ครบกำหนดทะเบียนประกัน / พรบ อีก ${daysDifference} วัน`
  
                  let noteMessage = `📲 หากครบกำหนดรับบริการดังกล่าว ลูกค้าสามารถแจ้งนัดหมายจองคิวล่วงหน้า 1- 2 วันผ่าน 𝗣𝗿𝗮𝗰𝗵𝗮𝗸𝗶𝗷 🌟\n`
                      noteMessage += `- - - - - - - - - - - - - -\n`
                      noteMessage += `🔖 ขออภัย ! หากเข้ามารับบริการเรียบร้อยแล้วนะคะ ... ทีมงานยินดีรับใช้และให้บริการค่ะ ❤️😊\n`
                      noteMessage += `🌟 เพราะเราคือบริษัทรถยนต์ที่ซื่อสัตย์และรับผิดชอบที่สุด 🌟\n`
                      noteMessage += `หากต้องการจองคิวสามารถคลิ๊กที่ปุ่มด้านล่าง`
              
                  const reqNotificationInApp = {
                      data: {
                          phone: modifiedData[i].phone,
                          type: daysDifference === 1 ? "notificationAppointment" : "notification",
                          title: `🔔 แจ้งเตือนล่วงหน้า ❝ ครบกำหนดทะเบียนประกัน / พรบที่ศูนย์บริการประชากิจฯ ❞ ค่ะ`,
                          detail: message,
                          note: noteMessage
                      }
                  };            
  
                  if (daysDifference === 30) {
                      console.log("ถึงกำหนด 30 วัน");
                      const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                  } else if (daysDifference === 7) {
                      console.log("ถึงกำหนด 7 วัน");
                      const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                  } else if (daysDifference === 1) {
                      console.log("ถึงกำหนด 1 วัน");
                      const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                  } else {
                      console.log("ยังไม่ต้องส่ง");
                  }   
              }

            return await responseSuccess({
                modifiedData
            }, 200);

        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Noti Insurance ${e}`, 407);
        }
    }
}

export let LifetimeAppointmentInsuranceModule = new NotiInsurance();
