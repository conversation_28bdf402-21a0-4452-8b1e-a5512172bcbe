import { log } from "console";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { NotificationModule } from "../../../modules/Notification/controller/notification-controller";

class NotiMiles {
    public async sendNotiMiles() {
        try {
            const postalCodes = [
                "22110",
                "22120",
                "22130",
                "22140",
                "22150",
                "22160",
                "22170",
                "22180",
                "22190",
                "22210",
                "22000",
              ];

            let res = await connectdocker.connect(
                DatabaseConfig.MIRAI,
                `SELECT Column24 AS phone, 
                MAX(Column18) AS name,
                MAX(Column27) AS car_reg,
                MAX(Column35) AS miles,
                MAX(create_time) AS last_date
                FROM AFSInvoiceHistoryReportSummary
                WHERE Column23 IN (?) AND
                      Column24 NOT LIKE '03%' AND Column24 NOT LIKE '%-%' AND Column24 NOT LIKE '02%'  AND Column24 NOT LIKE '00%'
                      AND create_time >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
                      AND LENGTH(Column24) = 10
                GROUP BY Column29
                `,
                [postalCodes]
            );

            if (res.length === 0) {
                return await responseError("Noti Miles not found", 404);
            }

            let currentDate = new Date(); // วันเวลาปัจจุบัน
        
            for (let i = 0; i < res.length; i++) {
                let date = new Date(res[i].last_date);
                date.setDate(date.getDate() + 180); //+6 เดือน
                
                let timeDifference = Math.abs(currentDate.getTime() - date.getTime());
                let daysDifference = Math.floor(timeDifference / (1000 * 3600 * 24));
                console.log(daysDifference);
                
                
                let message = `🔸 คุณ :: ${res[i].name}\n`
                    message += `🔸 ทะเบียนรถ :: ${res[i].car_reg === undefined ? "ไม่ทราบข้อมูล" : res[i].car_reg}\n`
                    message += `⏰ ครบกำหนดตรวจเช็กระยะ อีก ${daysDifference} วัน`

                let noteMessage = `📲 หากครบกำหนดรับบริการดังกล่าว ลูกค้าสามารถแจ้งนัดหมายจองคิวล่วงหน้า 1- 2 วันผ่าน 𝗣𝗿𝗮𝗰𝗵𝗮𝗸𝗶𝗷 🌟\n`
                    noteMessage += `- - - - - - - - - - - - - -\n`
                    noteMessage += `🔖 ขออภัย ! หากเข้ามารับบริการเรียบร้อยแล้วนะคะ ... ทีมงานยินดีรับใช้และให้บริการค่ะ ❤️😊\n`
                    noteMessage += `🌟 เพราะเราคือบริษัทรถยนต์ที่ซื่อสัตย์และรับผิดชอบที่สุด 🌟\n`
                    noteMessage += `หากต้องการจองคิวสามารถคลิ๊กที่ปุ่มด้านล่าง`
            
                const reqNotificationInApp = {
                    data: {
                        phone: res[i].phone,
                        type: daysDifference === 1 ? "notificationAppointment" : "notification",
                        title: `🔔 แจ้งเตือนล่วงหน้า ❝ ครบกำหนดตรวจเช็กตามระยะที่ศูนย์บริการประชากิจฯ ❞ ค่ะ`,
                        detail: message,
                        note: noteMessage
                    }
                };            

                if (daysDifference === 30) {
                    console.log("ถึงกำหนด 30 วัน");
                    const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                } else if (daysDifference === 7) {
                    console.log("ถึงกำหนด 7 วัน");
                    const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                } else if (daysDifference === 1) {
                    console.log("ถึงกำหนด 1 วัน");
                    const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                } else {
                    console.log("ยังไม่ต้องส่ง");
                }   
            }
        
            return await responseSuccess({
                res
            }, 200);

        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Noti Miles ${e}`, 407);
        }
    }
}

export let LifetimeAppointmentMilesModule = new NotiMiles();
