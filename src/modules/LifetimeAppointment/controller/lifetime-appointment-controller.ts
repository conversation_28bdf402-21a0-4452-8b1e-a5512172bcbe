import { getPhoneDataFromFirestore, parseFirestoreData, insertPhoneToFirestoreMenuMyCar, updatePhoneInFirestoreMenuMyCar } from "help/sendNotificationFCM";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

declare const GET_ENCYPT_KEY: string;

class LifeTimeAppointment {
  public async getLifetimeAppointment(req: any) {
    try {
      let bodyData = await req.data;
      const carList: any[] = [];

      const resFirebase = await getPhoneDataFromFirestore(bodyData.phone, "myCar/EcnyzdWmCIxBrRnh5QTk");
      
      const sqlCar = `SELECT 
          si.running, 
          si.name, 
          si.phone, 
          si.reg, 
          si.engin, 
          si.create_time
        FROM sa_image si
        INNER JOIN (
          SELECT engin, MAX(running) AS max_running
          FROM sa_image
          WHERE phone IN (?)
          GROUP BY engin
        ) max_results
        ON si.engin = max_results.engin AND si.running = max_results.max_running;
      `

      let resCar = await connectdocker.connect(DatabaseConfig.MIRAI, sqlCar, [
        [bodyData.phone],
      ]);
      
      if (resFirebase != false && resFirebase.documents.length === resCar.length) {
        // แปลงข้อมูล Firestore เป็นรูปแบบที่ใช้งานง่าย
        var dataOut = [];
        for (var i = 0; i < resFirebase.documents.length; i++) {
          const parsedData = parseFirestoreData(resFirebase.documents[i].fields);
          dataOut.push(parsedData);
        }

        // ตรวจสอบ create_time กับ dataUpdate สำหรับทุกคู่
        let allFirebaseNewer = true;
        for (let i = 0; i < resCar.length; i++) {
          const createTime = resCar[i]?.create_time ? new Date(resCar[i].create_time) : new Date(0);
          const dataUpdate = dataOut[i]?.dataUpdate ? new Date(dataOut[i].dataUpdate) : new Date(0);

          console.log(`Comparing index ${i}: createTime=${createTime}, dataUpdate=${dataUpdate}`);
          console.log(`createTime < dataUpdate: ${createTime < dataUpdate}`);

          if (createTime >= dataUpdate) {
            allFirebaseNewer = false;
            break; // ถ้ามีคู่ใดคู่หนึ่งที่ create_time >= dataUpdate ให้ออกจาก loop
          }
        }

        // ถ้าทุกคู่มี create_time < dataUpdate ให้คืนข้อมูลจาก Firestore
        if (allFirebaseNewer) {
          console.log("Returning Firestore data");
          return await responseSuccess(
            {
              carList: dataOut,
            },
            200
          );
        }
      } else {
        console.log("Processing resCar data");
      }

      if (resCar.length === 0) {
        return await responseError("LifetimeAppointment not found", 404);
      }

      for (const item of resCar) {
        carList.push({
          running: item.running,
          name: item.name,
          phone: item.phone,
          reg: item.reg,
          machine_number: item.engin,
          miles: [],
          rustproof: [],
          insurance: [],
          pmg: [],
          dataUpdate: new Date().toISOString()
        });
      }

      for (const car of carList) {
        const sqlContract = `SELECT BCT_CONTRACT.colorName, BCT_CONTRACT.colorCode, BCT_CONTRACT.carCode, MarginSale.car_model, MarginSale.car_model_sa, MarginSale.car_type
                FROM BCT_CONTRACT LEFT JOIN MarginSale 
                ON BCT_CONTRACT.carCode = MarginSale.carCode
                WHERE BCT_CONTRACT.engineCode IN (?)`;

        let resContract = await connectdocker.connect(
          DatabaseConfig.BCT_AMS1,
          sqlContract,
          [car.machine_number]
        );

        car.colorCode = resContract[0]?.colorCode ?? "";
        car.colorName = resContract[0]?.colorName ?? "";
        car.carCode = resContract[0]?.carCode ?? "";
        car.car_model = resContract[0]?.car_model ?? "";
        car.car_model_sa = resContract[0]?.car_model_sa ?? "";
        car.car_type = resContract[0]?.car_type ?? "";

        if (car.carCode !== "" && car.colorCode !== "") {
          let resCarPic = await connectdocker.connect(
            DatabaseConfig.webPKG,
            `SELECT *
                            FROM CarForPKG
                            LEFT JOIN CarColorForPKG ON CarForPKG.car_id = CarColorForPKG.car_id
                            LEFT JOIN CarImageForPKG ON CarColorForPKG.color_id = CarImageForPKG.color_id
                            WHERE CarForPKG.model_name = ? AND CarColorForPKG.name = ?`,
            [car.car_model, car.colorCode]
          );
          car.car_image = resCarPic[0]?.image_url ?? "";
          console.log(resCarPic);
        }

        const sql = `SELECT AFSInvoiceHistoryDetail.Column56, AFSInvoiceHistoryDetail.Column5, AFSInvoiceHistoryDetail.Column23, AFSInvoiceHistoryDetail.Column11,
                AFSInvoiceHistoryReportSummary.Column35, AFSInvoiceHistoryReportSummary.Column28
                FROM AFSInvoiceHistoryDetail LEFT JOIN AFSInvoiceHistoryReportSummary
                ON AFSInvoiceHistoryDetail.Column56 = AFSInvoiceHistoryReportSummary.Column114
                WHERE AFSInvoiceHistoryReportSummary.Column29 = ?
                ORDER BY AFSInvoiceHistoryReportSummary.running DESC`;

        let res = await connectdocker.connect(DatabaseConfig.MIRAI, sql, [
          car.machine_number,
        ]);

        if (res.length == 0) {
          return await responseError("LifetimeAppointment not found", 404);
        }

        // Initialize latestItem as the first item by default
        let latestItem = res[0];

        // Loop through `res` to find the first item where Column23 starts with "เช็กระยะ"
        for (const item of res) {
          if (item.Column23.startsWith("เช็กตามระยะ")) {
            console.log("checked");
            latestItem = item;
            break; // Stop the loop once we find the first matching item
          }
        }

        // Set car_id and add mileage details for the selected latestItem
        car.car_id = latestItem.Column28 ?? "";

        car.miles.push({
          mileage_detail: latestItem.Column23 ?? "",
          mileage: latestItem.Column35 ?? "",
          mileage_date: latestItem.Column11 ?? "",
        });

        for (const item of res) {
          let foundItem = null;
          if (item.Column23.includes("สนิม")) {
            car.rustproof.push({
              rust_detail: item.Column23 ?? "",
              rust_date: item.Column11 ?? "",
            });
            foundItem = item;
            break;
          }
        }

        const sqlInsurance = `SELECT running, insurance, isr_date_start_insurance, isr_type_date_end_insurance FROM BCT_INSURANCE WHERE engineCode = ? ORDER BY running DESC`;

        let resInsurance = await connectdocker.connect(
          DatabaseConfig.BCT_AMS1,
          sqlInsurance,
          [car.machine_number]
        );

        for (const item of resInsurance) {
          let foundItem2 = null;
          car.insurance.push({
            insurance: resInsurance[0].insurance ?? "",
            insurance_date: resInsurance[0].isr_date_start_insurance ?? "",
            insurance_date_end:
              resInsurance[0].isr_type_date_end_insurance ?? "",
          });
          foundItem2 = item;
          break;
        }

        const sqlPMG = `SELECT job_head.job_id, invoice_head.invoice_date
                FROM job_head LEFT JOIN invoice_head 
                ON job_head.job_id = invoice_head.job_id
                WHERE job_head.car_engine = ? AND
                (job_head.job_note LIKE '%เคลือบแก้ว%' OR job_head.job_note LIKE '%บำรุงผิวแก้ว%')
                ORDER BY job_head.running DESC`;

        let resPMG = await connectdocker.connect(
          DatabaseConfig.BCT_PMG_NEW,
          sqlPMG,
          [car.machine_number]
        );

        for (const item of resPMG) {
          let foundItem3 = null;
          car.pmg.push({
            pmg_job_id: resPMG[0].job_id ?? "",
            pmg_date: resPMG[0].invoice_date ?? "",
          });
          foundItem3 = item;
          break;
        }
      }

      if (resFirebase == false) {
        for (let i = 0; i < carList.length; i++) {
          await insertPhoneToFirestoreMenuMyCar(bodyData.phone, "myCar/EcnyzdWmCIxBrRnh5QTk", carList[i]);
        }
      } else {
        for (let i = 0; i < carList.length; i++) {
          const docId = resFirebase.documents[i].name.split('/');
          await updatePhoneInFirestoreMenuMyCar(bodyData.phone, "myCar/EcnyzdWmCIxBrRnh5QTk", docId[8], carList[i]);
        }
      }
      
      return await responseSuccess(
        {
          carList: carList,
        },
        200
      );
    } catch (e) {
      console.log("error");
      console.log(e);
      return await responseError("ERROR :: getLifetimeAppointment", 407);
    }
}

  public async getDataOnday(req: any) {
    try {
      const bodyData = await req.data;

      console.log("Received bodyData:", bodyData);

      // Validate the `whoami` key
      if (bodyData.whoami !== GET_ENCYPT_KEY) {
        console.log(bodyData);
        
        return await responseError("ERROR :: who are bro", 409);
      }

      // Validate `start_time` and `end_time`
      if (!bodyData.start_time || !bodyData.end_time) {
        return await responseError("ERROR :: 'start_time' and 'end_time' are required", 400);
      }

      // Format dates for SQL
      const startOfDay = new Date(bodyData.start_time).toISOString().slice(0, 19).replace('T', ' ');
      const endOfDayStr = new Date(bodyData.end_time).toISOString().slice(0, 19).replace('T', ' ');

      // SQL query
      const sql = `
        SELECT running, phone, create_time
        FROM sa_image
        WHERE create_time BETWEEN ? AND ?`;

      // Execute query
      const res = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        sql,
        [startOfDay, endOfDayStr]
      );

      // Check if data exists
      if (res.length === 0) {
        return await responseSuccess({ message: "No data found for the specified range" }, 201);
      }

      // Return result
      return await responseSuccess({ data: res }, 200);
    } catch (e) {
      console.error("Error in getDataOnday:", e);
      return await responseError(`ERROR :: ${e}`, 407);
    }
  }
}

export let LifetimeAppointmentModule = new LifeTimeAppointment();
