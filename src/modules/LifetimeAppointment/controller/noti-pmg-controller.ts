import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { NotificationModule } from "../../../modules/Notification/controller/notification-controller";

class NotiPMG {
    public async sendNotiPMG() {
        try {
            const postalCodes = [
                "22110",
                "22120",
                "22130",
                "22140",
                "22150",
                "22160",
                "22170",
                "22180",
                "22190",
                "22210",
                "22000",
            ];
            const sqlQuery = `
                        SELECT 
                            job.job_id, 
                            inv.invoice_date AS last_date,
                            inv.mobile AS phone,
                            inv.firstname AS firstname,
                            inv.lastname AS lastname,
                            job.job_note
                        FROM 
                            job_head job 
                        LEFT JOIN 
                             invoice_head inv ON job.job_id = inv.job_id
                        WHERE 
                            inv.create_time >= DATE_SUB(NOW(), INTERVAL 2 YEAR) AND 
                            (job.job_note LIKE '%เคลือบแก้ว%' OR job.job_note LIKE '%บำรุงผิวแก้ว%')
                        GROUP BY 
                            inv.car_engine
                    `;

                    let result = await connectdocker.connect(DatabaseConfig.BCT_PMG_NEW, sqlQuery, [postalCodes]);

           

                    if (result.length === 0) {
                        return await responseError("Notification PMG not found", 404);
                    }

                    const modifiedData = result.map((item:any) => ({
                        ...item,
                        phone: item.phone.replace(/[-\s]/g, ""),
                        name: item.firstname + " " + item.lastname,
                        job: item.job_note.includes('เคลือบแก้ว') ? 'เคลือบแก้ว' : 'บำรุงผิวแก้ว'
                    }));
            
                    let currentDate = new Date(); 
            

                    for (let i = 0; i < modifiedData.length; i++) {
                        let date = new Date(modifiedData[i].last_date);
                        date.setDate(date.getDate() + 120);
                        let timeDifference = Math.abs(currentDate.getTime() - date.getTime());
                        let daysDifference = Math.floor(timeDifference / (1000 * 3600 * 24));
                        let message = `🔸 คุณ :: ${modifiedData[i].name}\n`;
                        message += `🔸 ทะเบียนรถ :: ${modifiedData[i].car_reg === undefined ? "ไม่ทราบข้อมูล" : modifiedData[i].car_reg}\n`;
                        message += `⏰ ครบกำหนด ${modifiedData[i].job} อีก ${daysDifference} วัน`;
                        let noteMessage = `📲 หากครบกำหนดรับบริการดังกล่าว ลูกค้าสามารถแจ้งนัดหมายจองคิวล่วงหน้า 1- 2 วันผ่าน 𝗣𝗿𝗮𝗰𝗵𝗮𝗸𝗶𝗷 🌟\n`;
                        noteMessage += `- - - - - - - - - - - - - -\n`;
                        noteMessage += `🔖 ขออภัย ! หากเข้ามารับบริการเรียบร้อยแล้วนะคะ ... ทีมงานยินดีรับใช้และให้บริการค่ะ ❤️😊\n`;
                        noteMessage += `🌟 เพราะเราคือบริษัทรถยนต์ที่ซื่อสัตย์และรับผิดชอบที่สุด 🌟\n`;
                        noteMessage += `หากต้องการจองคิวสามารถคลิ๊กที่ปุ่มด้านล่าง`;
            
                        const reqNotificationInApp = {
                            data: {
                                type: daysDifference === 1 ? "notificationAppointment" : "notification",
                                title: `🔔 แจ้งเตือนล่วงหน้า ❝ ครบกำหนดเคลือบแก้วที่ศูนย์บริการประชากิจฯ ❞ ค่ะ`,
                                detail: message,
                                note: noteMessage
                            }
                        };

                    if (daysDifference === 30) {
                        console.log("ถึงกำหนด 30 วัน");
                        const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                    } else if (daysDifference === 7) {
                        console.log("ถึงกำหนด 7 วัน");
                        const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                    } else if (daysDifference === 1) {
                        console.log("ถึงกำหนด 1 วัน");
                        const resNotificationInApp = await NotificationModule.sendNotificationInAppPMS(reqNotificationInApp);
                    } else {
                        console.log("ยังไม่ต้องส่ง");
                    }
            }

            return await responseSuccess({
                modifiedData
            }, 200);

        } catch (e) {
            console.log(e);
            return await responseError(`ERROR :: Noti PMG ${e}`, 407);
        }
    }
}

export let LifetimeAppointmentPMGModule = new NotiPMG();
