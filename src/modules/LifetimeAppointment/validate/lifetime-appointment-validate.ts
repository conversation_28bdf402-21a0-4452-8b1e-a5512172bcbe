import { responseError } from "../../../help/response";
import <PERSON><PERSON> from "joi";

export class ValidateLifetimeAppointment {

    public async ValidateLifetimeAppointment(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                    phone: Joi.string(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async CheckWho(req: any){
        try { 
            const params = await req.data;

            let schema = Joi.object({
                whoami: Joi.string(),
                start_time: Joi.any(),
                end_time: Joi.any(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationLifetimeAppointment = new ValidateLifetimeAppointment();