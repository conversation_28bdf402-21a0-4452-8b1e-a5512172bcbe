export interface MemberType {
    id:                    number;
    nickname:              string;
    fullname:              string;
    username:              string;
    password:              string;
    phone_firebase:        string;
    firstname:             string;
    lastname:              string;
    mobile:                string;
    tokenMessage:          string;
    accept_agreement:      string;
    birthday:              string;
    address:               string;
    address_number:        string;
    address_moo:           string;
    address_road:          string;
    address_tumbol:        string;
    address_amphur:        string;
    address_province:      string;
    address_zipcode:       string;
    email:                 string;
    line_id_temp:          string;
    user_url:              string;
    countLogin:            number;
    create_user:           string;
    idcard:                string;
    displayName:           string;
    userID_line:           string;
    userID_fb:             string;
    userID_apple:          string;
    link_accounts:         number;
    link_accounts_connect: string;
    profile_picture:       string;
    username_connect:      string;
    facebook_connect:      string;
    apple_connect:         string;
    line_connect:          string;
    roleId:                string;
    id_ref_only:           string;
    id_ref_other:          string;
    location_home:         string;
    mr_code:               string;
    bank_name:             string;
    book_bank_no:          string;
    book_bank_name:        string;
    full_name:             string;
    id_card:               string;
    phone_number:          string;
    career:                string;
    career_note:           string;
    business_name:         string;
}