import Joi from "joi";
import {Request} from "itty-router";
import {responseError} from "../../../help/response";

export class ValidateMember {

    public async ValidateGetMember(req: any) {
        try {
            const paramsGet = await req.data;
            
            let schemaGet = Joi.object({
                id: Joi.string().required()
            });

            const {error} = await schemaGet.validate(paramsGet);

            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

    public async ValidateUpdateProfile(req: any) {
        try {
            const paramsUpdate = await req.data;
            let schema = Joi.object({
                address: Joi.any(),
                fullname: Joi.any(),
                lastname: Joi.any(),
                idcard: Joi.any(),
                birthday: Joi.any(),
                number: Joi.any(),
                moo: Joi.any(),
                road: Joi.any(),
                tumbol: Joi.any(),
                amphur: Joi.any(),
                province: Joi.any(),
                zipcode: Joi.any(),
                email: Joi.any(),
                location_home: Joi.any(),
                id: Joi.string().required(),
            });

            const {error} = await schema.validate(paramsUpdate);

            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationMember = new ValidateMember();