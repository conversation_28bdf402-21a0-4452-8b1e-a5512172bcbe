import Joi from "joi";
import { responseError } from "../../../help/response";

export class ValidateAcceptAgreement {
  public async ValidateAcceptAgreement(req: any) {
    try {
      const params = await req.data;
      let schemaGet = Joi.object({
        id: Joi.string().required(),
      });

      const { error } = await schemaGet.validate(params);
      if (error) {
        return await responseError(
          `Wrong Parameter :: ${JSON.stringify(error.details)}`,
          407
        );
      }
    } catch (e) {
      console.log(e);
      return await responseError("Wrong Parameter", 407);
    }
  }
}

export let ValidationAcceptAgreement = new ValidateAcceptAgreement();
