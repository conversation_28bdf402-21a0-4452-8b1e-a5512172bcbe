import { Request } from "itty-router";
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { MemberType } from "../type/member-type";

class MemberController {
    public async getProfileAndMR(req: any) {
        try{
            let params = await req.params;
            // console.log(params);
            // let params = await validateGetProfile(req);
            
            const res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT customercenter.id, customercenter.nickname, customercenter.fullname, customercenter.username,
                    customercenter.password, customercenter.phone_firebase, customercenter.firstname, customercenter.lastname,
                    customercenter.mobile, customercenter.tokenMessage, customercenter.accept_agreement, customercenter.birthday,
                    customercenter.address, customercenter.address_number, customercenter.address_moo, customercenter.address_road,
                    customercenter.address_tumbol, customercenter.address_amphur, customercenter.address_province, customercenter.address_zipcode,
                    customercenter.email, customercenter.line_id_temp, customercenter.user_url, customercenter.countLogin, customercenter.create_user,
                    customercenter.idcard, customercenter.displayName, customercenter.userID_line, customercenter.userID_fb, customercenter.userID_apple,
                    customercenter.link_accounts, customercenter.link_accounts_connect, customercenter.profile_picture, customercenter.username_connect,
                    customercenter.facebook_connect, customercenter.apple_connect, customercenter.line_connect, customercenter.roleId, 
                    customercenter.id_ref_only, customercenter.id_ref_other, customercenter.location_home,
                    mr_pms_center.mr_code, mr_pms_center.bank_name, mr_pms_center.book_bank_no, mr_pms_center.book_bank_name, mr_pms_center.full_name,
                    mr_pms_center.id_card, mr_pms_center.phone_number, mr_pms_center.career, mr_pms_center.career_note, mr_pms_center.business_name
                   FROM customercenter LEFT JOIN mr_pms_center
                   ON customercenter.id = mr_pms_center.user_id
                   WHERE id = ? LIMIT 1`,
                [params.id.trim()]
            ) as MemberType[]

            return responseSuccess(res[0], 200);
        } catch (e){
            console.log(e);
            return await responseError("ERROR :: Get ProfileAndMR", 407);
        }
    }

    public async saveProfileByID(req: any) {
        try{
            const params = await req.paramsUpdate;
            let fullName = params.fullname + " " + params.lastname;
            let value = [
                params.address,
                fullName,
                params.fullname,
                params.lastname,
                params.idcard,
                params.birthday,
                params.number,
                params.moo,
                params.road,
                params.tumbol,
                params.amphur,
                params.province,
                params.zipcode,
                params.email,
                fullName,
                params.location_home,
                params.id
            ]
            const res = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `UPDATE customercenter
                         SET address          = ?,
                             displayName      = ?,
                             firstname        = ?,
                             lastname         = ?,
                             idcard           = ?,
                             birthday         = ?,
                             address_number   = ?,
                             address_moo      = ?,
                             address_road     = ?,
                             address_tumbol   = ?,
                             address_amphur   = ?,
                             address_province = ?,
                             address_zipcode  = ?,
                             email            = ?,
                             fullname         = ?,
                             location_home    = ?
                         WHERE id = ?`,
                [value]
            )
        }catch (e){
            console.log(e);
            return await responseError("ERROR :: Create License Plate", 407);
        }
    }
}

export let MemberModule = new MemberController();
