import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";

class updateAgreementController {
  public async updateAgreement(req: any, res: any) {
    try {
      let bodyData = await req.data;
      let resUpdateAgreement = await connectdocker.connect(
        DatabaseConfig.webPKG,
        `UPDATE customercenter SET accept_agreement = ? WHERE id = ?`,
        ["Y", bodyData.id]
      );

      console.log(resUpdateAgreement);
      if (resUpdateAgreement.length === 0) {
        return await responseError("Update Agreement not found", 404);
      }

      return await responseSuccess(resUpdateAgreement, 200);
    } catch (e) {
      return await responseError(`ERROR :: Update Agreement => ${e}`, 407);
    }
  }
}

export let UpdateAgreementModule = new updateAgreementController();
