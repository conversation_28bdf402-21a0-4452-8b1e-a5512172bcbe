import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";

class checkProfile {
    public async checkProfile(req: any) {
        try {
            let bodyData = await req.data;
            let resCheckProfile = await connectdocker.connect(
                DatabaseConfig.webPKG,
                `SELECT id, roleId, mobile FROM customercenter WHERE mobile = ? LIMIT 1`,
                [bodyData.phone]
            );

            console.log(resCheckProfile);
            if (resCheckProfile.length === 0) {
                return await responseError("Profile not found", 404);
            }
            
            return await responseSuccess(resCheckProfile[0], 200);

        } catch (e) {
            return await responseError(`ERROR :: Check Profile => ${e}`, 407);
        }
    }
}

export let CheckProfileModule = new checkProfile();