import { responseError, responseSuccess } from "../../../help/response";

class POIPaylike {
    public async POIPaylike(req: any) {
        try {
            let bodyData = await req.data;
            console.log(bodyData);
            

                const newRequest = new Request(`${API_URL_LIKEPOINT}/transactions-activity/pay-poi-in-app`, {
                    body: JSON.stringify(bodyData),
                    headers: {
                    "Content-Type": "application/json",
                    "x-api-key": API_KEY_LIKEPOINT
                    },
                    method: "POST",
                });
                const res = await fetch(newRequest);
                const resSaveActivity = await res.json()

                if(resSaveActivity.data[1].statusPay == "Completed") { 

                    return await responseSuccess(resSaveActivity, 200);
                }

            return await responseError("Wrong Parameter", 407);
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let POIPaylikeModule = new POIPaylike();