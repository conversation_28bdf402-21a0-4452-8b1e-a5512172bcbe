import Joi from "joi";
import {responseError} from "../../../help/response";

export class ValidatePOIPayLikepoint {
    
    public async ValidatePOIPayLikepoint(req: any){
        try {
            const params = await req.data;
            let schema = Joi.object({
                phone: Joi.required(),
                activityID: Joi.any().required(),
                firstName: Joi.any().required(),
                lastName: Joi.any().required(),
                merchantID: Joi.required(),
            });

            const {error} = await schema.validate(params);

            console.log(error);
            console.log("=-=-=-=-=-=-=");
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }

        } catch (e){
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }

}

export let ValidationPOIPayLikepoint = new ValidatePOIPayLikepoint();