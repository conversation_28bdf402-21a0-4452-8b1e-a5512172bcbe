import Joi from "joi";
import {Request} from "itty-router";
import {responseError} from "../../../help/response";

export class ValidateBrokenCar {
    public async ValidateBrokenCar(req: any){
        try {
            const params = await req.data;
            let schema = Joi.object({
                type_member: Joi.string().required(),
                user_id: Joi.any(),
                phone: Joi.string().required(),
                location: Joi.string().required(),
                car_type: Joi.string().required(),
                technician_Number: Joi.string(),
            })

            const {error} = await schema.validate(params);
            console.log(error);
            if(error) {
                return await responseError(`Wrong Parameter :: ${JSON.stringify(error.details)}`, 407);
            }
            
        } catch (e) {
            console.log(e);
            return await responseError("Wrong Parameter", 407);
        }
    }
}

export let ValidationBrokenCar = new ValidateBrokenCar();