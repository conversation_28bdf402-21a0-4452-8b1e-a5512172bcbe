import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
import { responseError, responseSuccess } from "../../../help/response";
import { lineNotify } from "../../../help/line-notify";
import { TGNotify } from "../../../help/TG-notify";

class BrokenCarController {

    public async createBrokenCar(req: any){
        try {
            let bodyData = await req.data;

            const now = new Date(Date.now()).toLocaleString('en-US', {timeZone: 'Asia/Bangkok',});
              
            let value = {
                date_report: now,
                type_member: bodyData.type_member,
                user_id: bodyData.user_id,
                phone: bodyData.phone,
                location: bodyData.location,
                car_type: bodyData.car_type,
                channel: bodyData.channel ?? "Mapp",
            }
            var dataInsert = await connectdocker.connect(
                DatabaseConfig.MApp_PMS,
                // `INSERT INTO report_broken_car SET ?`,
                "INSERT INTO report_broken_car (date_report, type_member, user_id, phone, location, car_type, channel) VALUES (?, ?, ?, ?, ?, ?, ?)",
                [value.date_report, value.type_member, value.user_id, value.phone, value.location, value.car_type, value.channel]
            )

            const running = dataInsert["lastInsertId"];
            if (bodyData.user_id != "") {
                var resName = await connectdocker.connect(
                    DatabaseConfig.webPKG,
                    `SELECT fullname FROM customercenter WHERE id = ${bodyData.user_id}`,
                    []
                )
            }

            let fullName = "";
            if (resName.length == 0 || resName[0]["fullname"] == null) {
                fullName = "ไม่ทราบชื่อลูกค้า";
            } else {
                fullName = resName[0]["fullname"];
            }
            
            let message = "🔔 แจ้งเตือน มีลูกค้าใช้เมนูรถเสีย\n";
                message += `📱 ช่องทาง : MApp\n`;
                message += `📞 เบอร์ช่าง : ${bodyData.technician_Number}\n`;
                message += `👫 ชื่อลูกค้า : ${fullName}\n`;
                message += `📞 เบอร์โทรลูกค้า : ${bodyData.phone}\n`;
                message += `📍 พิกัดรถเสียของลูกค้า https://www.google.com/maps/search/?api=1&query=${bodyData.location}\n`;
                message += `👉 ลิงค์รายงานความก้าวหน้า https://devdev.prachakij.com/PMS_Appointment/broken_car.php?id=${running}`;
                
                // var tokenTest = "aBUN26yuGxcOZ6XNlRQT39DYYxu9ltx7qf985kLuGjY";
                var tokenLinePMS_www_service = "Egw0UJMIXwAm0XgI5RlxW1keNLyDafqYyLYfgQolWvv";
                var tokenLinePMSTech = "5CoFMgCLwt7miXXtoLbFvju95q7BHsBRYBwn101JP7U";

                var tokenTGTech = "-1001662401008";
            await lineNotify(tokenLinePMS_www_service,message); ///ส่ง line
            await lineNotify(tokenLinePMSTech,message); ///ส่ง line
            
            await TGNotify(message, tokenTGTech);

            
        } catch (e){
            console.log(e);
            return await responseError("ERROR :: Create License Plate", 407);
        }
    }
}

export let BrokenCarModule = new BrokenCarController();