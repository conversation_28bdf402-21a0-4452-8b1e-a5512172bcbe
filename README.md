# API ALL PMS Power by cloudflare workers

# RUN NODE SERVER FOR DEVELOPMENT
``npm run dev``

## HOW TO DEPLOY
[![An old rock in the desert](https://media.giphy.com/media/PApUm1HPVYlDNLoMmr/giphy.gif "report file")](https://media.giphy.com/media/PApUm1HPVYlDNLoMmr/giphy.gif)

# 1. Install wrangler
``npm install -g @cloudflare/wrangler``
# 2. Login to your Cloudflare account
``wrangler login``
# 3. Publish your worker
``wrangler publish --env production
# 4. Enjoy

