{"name": "api-pms-cloudflare-workers", "version": "1.0.0", "description": "API ALL PMS power by cloudflare workers", "module": "./dist/index.mjs", "scripts": {"build": "esbuild --bundle --sourcemap --outfile=dist/index.mjs --minify --format=esm ./src/index.js --external:*.wasm --inject:./src/deno/workers-override.ts", "dev": "wrangler dev --local", "dev-local": "wrangler dev --local --env production", "publish": "npx wrangler publish", "format": "prettier --write  '*.{json,js}' 'src/**/*.{js,ts}' 'test/**/*.{js,ts}'", "lint": "eslint --max-warnings=0 src && prettier --check '*.{json,js}' 'src/**/*.{js,ts}' 'test/**/*.{js,ts}'", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js"}, "author": "TEAM AGS APP", "license": "MIT OR Apache-2.0", "eslintConfig": {"root": true, "extends": ["typescript", "prettier"]}, "devDependencies": {"@cloudflare/workers-types": "^3.14.1", "@types/cors": "^2.8.13", "@types/jest": "^28.1.6", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "eslint-config-typescript": "^3.0.0", "jest": "^28.1.3", "jest-environment-miniflare": "^2.6.0", "miniflare": "^2.6.0", "prettier": "^2.7.1", "ts-jest": "^28.0.7", "typescript": "^4.7.4", "wrangler": "^2.0.24"}, "dependencies": {"@agilesoft/type_ags_authrest2": "^1.1.8", "aws-sdk": "^2.1405.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "itty-router": "^2.6.6", "joi": "^17.9.2", "moment": "^2.30.1", "uniqid": "^5.4.0"}}